import axios, { AxiosRequestConfig } from "axios";
import { debounce } from "lodash";
import moment from "moment";
import { FC, Fragment, useCallback, useEffect, useState } from "react";
import { Badge, Button, Card, Col, Form, InputGroup, Modal, Row, Table } from "react-bootstrap";
import { Link } from "react-router-dom";
import SimpleBar from "simplebar-react";
import Swal from "sweetalert2";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import {
  useLazyExportCheckoutErrorsReportQuery,
  useLazyGetCheckoutFulfilOrderLineItemsQuery,
  useLazyGetShippingCheckoutReportQuery,
  useLazySelectShippingAdminsQuery
} from "../../../../services/report/shipping_checkout";
import { EShippingCheckoutItemScanType, EShippingCheckoutStatus } from "../../../../utils/constant/order";
import { getAllErrorMessages } from "../../../../utils/errors";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import LazySelect from "../../../../components/lazy-select/lazy-select";
import { SHIPPING_CHECKOUT_REPORT_TYPE } from "../../../../utils/constant/shipping-checkout";

interface ManagementShippingCheckoutReportProps { }

const ManagementShippingCheckoutReport: FC<ManagementShippingCheckoutReportProps> = () => {

  const [isLoading, setIsLoading] = useState(false)

  const [
    trigger,
    // { data, isLoading: isLoadingReports, isFetching }
  ] = useLazyGetShippingCheckoutReportQuery()
  const [shippingCheckoutReports, setShippingCheckoutReports] = useState<TShippingCheckoutReport[]>([])

  const [fulfilNumber, setFulfilNumber] = useState('')
  const debouncedSetFulfilNumber = useCallback(debounce((input) => setFulfilNumber(input), 1000), [])

  const [skuBarcode, setSkuBarcode] = useState('')
  const debouncedSetSkuBarcode = useCallback(debounce((input) => setSkuBarcode(input), 1000), [])

  const [page, setPage] = useState(1)
  const [limit] = useState(20)
  const [lastPage, setLastPage] = useState(20)
  const [total, setTotal] = useState(20)

  const [filters, setFilters] = useState<{
    startDate: string,
    endDate: string,
    checkerIds: string[],
  }>({
    startDate: moment().subtract(1, 'week').format('YYYY-MM-DD'),
    endDate: moment().format('YYYY-MM-DD'),
    checkerIds: [],
  })

  const [compareDate, setCompareDate] = useState(moment().subtract(2, 'weeks').format('YYYY-MM-DD'))

  useEffect(() => {
    setIsLoading(true)
    // @ts-ignore
    trigger({ page, limit, fulfilNumber, skuBarcode, ...filters, compareDate, })
      .unwrap()
      .then((res) => {
        setShippingCheckoutReports(res.data || [])

        setLastPage(res.meta.lastPage)
        setTotal(res.meta.total)
      })
      .catch((error) => {
        Swal.fire(getAllErrorMessages(error).messages[0], '', 'error')
      })
      .finally(() => {
        setIsLoading(false)
      })

  }, [page, fulfilNumber, skuBarcode, filters])

  const [reportType, setReportType] = useState(SHIPPING_CHECKOUT_REPORT_TYPE.ERRORS)
  const [exportCheckoutErrorsReport] = useLazyExportCheckoutErrorsReportQuery()
  const handleExportClick = (event) => {
    if (event) { event.preventDefault() }

    setIsLoading(true)
    exportCheckoutErrorsReport({
      reportType,
      fulfilNumber,
      ...filters,
    })
      .then((res) => {
        if (res.data) {
          const headers = { 'Content-Type': 'blob' };
          const config: AxiosRequestConfig = {
            method: 'GET',
            url: res.data.url,
            responseType: 'arraybuffer',
            headers
          };

          axios(config)
            .then((response) => {
              const url = URL.createObjectURL(new Blob([response.data]));
              const link = document.createElement('a');
              link.href = url;
              link.download = res.data?.filename || ""
              link.click();
            })
            .catch((error) => console.log(error))
        }
      })
      .catch((error) => console.log(error))
      .finally(() => setIsLoading(false))
  }

  const [selectShippingAdmins] = useLazySelectShippingAdminsQuery()

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Card className='custom-card'>
        <Card.Header>
          <CardHeaderWithBack
            title="Manage Shipping Checkout Report"
            route=""
          />
          <Card.Subtitle>
            <InputGroup>
              <Button
                hidden={!hasPermission(ACTION.EXPORT, RESOURCE.SHIPPING_CHECKOUT_REPORT)}
                variant="success-light"
                onClick={handleExportClick}
              >
                Export<i className="bi bi-download ms-2" />
              </Button>
              {
                reportType == SHIPPING_CHECKOUT_REPORT_TYPE.FIXES &&
                <Form.Control
                  type='date'
                  className="w-25"
                  value={compareDate}
                  onChange={(event) => setCompareDate(event.target.value)}
                />
              }
              <Form.Select onChange={(e) => setReportType(e.target.value)}>
                {
                  Object.values(SHIPPING_CHECKOUT_REPORT_TYPE)
                    .filter((e: any) => !isNaN(parseInt(e)))
                    .map((type, index) => (
                      <option
                        key={index}
                        value={type}
                      >
                        <span className="text-capitalize">
                          {type}
                        </span>
                      </option>
                    ))
                }
                <option value='errors'>Errors</option>
                <option value='fixes'>Fixes</option>
                <option value='dailies'>Dailies</option>
                <option value='personals'>Personals</option>
              </Form.Select>
            </InputGroup>
          </Card.Subtitle>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col lg={4}>
              <Form.Control
                type='search'
                placeholder="Fulfil Number"
                onChange={(event) =>
                  debouncedSetFulfilNumber(event.target.value)
                }
              />
            </Col>
            <Col lg={4}>
              <Form.Control
                type='search'
                placeholder="Sku / Barcode"
                onChange={(event) =>
                  debouncedSetSkuBarcode(event.target.value)
                }
              />
            </Col>
            <Col lg={4}>
              <InputGroup>
                <InputGroup.Text>
                  Checker
                </InputGroup.Text>
                <div style={{ width: '100%' }}>
                  <LazySelect
                    isClearable
                    selectionFunction={selectShippingAdmins}
                    label={value => value.name || value.username}
                    getSelectedOptions={(value) => setFilters({
                      ...filters,
                      checkerIds: value ? [value.id] : []
                    })}
                  />
                </div>
              </InputGroup>
            </Col>
          </Row>
          <Row>
            <Col lg={3}>
              <InputGroup>
                <InputGroup.Text>
                  From
                </InputGroup.Text>
                <Form.Control
                  type='date'
                  value={filters.startDate}
                  onChange={(event) =>
                    setFilters({
                      ...filters,
                      startDate: event.target.value
                    })
                  }
                />
              </InputGroup>
            </Col>
            <Col lg={3}>
              <InputGroup>
                <InputGroup.Text>
                  To
                </InputGroup.Text>
                <Form.Control
                  type='date'
                  value={filters.endDate}
                  onChange={(event) =>
                    setFilters({
                      ...filters,
                      endDate: event.target.value
                    })
                  }
                />
              </InputGroup>
            </Col>
          </Row>
        </Card.Body>
      </Card>
      <Card>
        <Card.Body>
          <Table className="table table-bordered text-nowrap border-bottom">
            <thead>
              <tr>
                <th>Fulfil Number</th>
                <th>Status</th>
                <th>Started At</th>
                <th>Duration</th>
                <th>Checked By</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {shippingCheckoutReports.map((shippingCheckoutReport, index) => (
                <Fragment key={index}>
                  <ReadOnlyRow
                    shippingCheckoutReport={shippingCheckoutReport}
                  />
                </Fragment>
              ))}
            </tbody>
          </Table>
          <PaginationBar
            page={page}
            setPage={setPage}
            lastPage={lastPage}
            limit={limit}
            total={total}
          />
        </Card.Body>
      </Card>
    </Fragment >
  );
};

const ReadOnlyRow = ({
  shippingCheckoutReport
}: any) => {
  const getStatusDisplayType = (status: EShippingCheckoutStatus) => {
    switch (status) {
      case (EShippingCheckoutStatus.SHOW): {
        return 'info'
      }
      case (EShippingCheckoutStatus.START): {
        return "primary"
      }
      case (EShippingCheckoutStatus.CANCEL): {
        return "warning"
      }
      case (EShippingCheckoutStatus.FINISH): {
        return "success"
      }
      default: {
        return "dark"
      }
    }
  }

  const getStatusPill = (status: EShippingCheckoutStatus) => {
    const displayType = getStatusDisplayType(status)
    switch (status) {
      case (EShippingCheckoutStatus.SHOW): {
        return <Badge bg={displayType}>Show</Badge>
      }
      case (EShippingCheckoutStatus.START): {
        return <Badge bg={displayType}>Start</Badge>
      }
      case (EShippingCheckoutStatus.CANCEL): {
        return <Badge bg={displayType}>Cancel</Badge>
      }
      case (EShippingCheckoutStatus.FINISH): {
        return <Badge bg={displayType}>Finish</Badge>
      }
      default: {
        return <Badge bg={displayType}>{status}</Badge>
      }
    }
  }

  const [modalShown, setModalShown] = useState(false)

  const [hoveredCardId, setHoveredCardId] = useState('')
  const handleCardHover = (id: string) => {
    setHoveredCardId(
      id == hoveredCardId
        ? ""
        : id
    )
  }
  const hoveredStatus = (id: string) => {
    return id == hoveredCardId ? 'border-info' : ''
  }

  const [clickedItemSku, setClickedItemSku] = useState('')
  const handleItemClick = (sku: string) => {
    setClickedItemSku(
      sku == clickedItemSku
        ? ""
        : sku
    )
  }
  const highlightedStatus = (sku: string) => {
    return sku == clickedItemSku ? 'bg-info-transparent' : ''
  }

  const [fulfilLines, setFulfilLines] = useState<any[] | null>(null)
  const [getFulfilLines, { isFetching: isFulfilFetching }] = useLazyGetCheckoutFulfilOrderLineItemsQuery()
  const handleModalShown = () => {
    if (fulfilLines) { return }

    getFulfilLines({ fulfilNumber: shippingCheckoutReport.fulfilNumber })
      .unwrap()
      .then((res) => {
        console.log(res);

        setFulfilLines(res)
      })
      .catch((error) => {
        Swal.fire('Error!', getAllErrorMessages(error).messages[0], 'error')
      })
  }

  const getItemScanTypeBadge = (item) => {
    let status = 'dark'
    switch (item.scanType) {
      case EShippingCheckoutItemScanType.SCAN: {
        status = 'success'
        break
      }
      case EShippingCheckoutItemScanType.PASS: {
        status = 'info'
        break
      }
    }

    return <Badge bg={status}>
      <span className="text-capitalize">
        {item.scanType}
      </span>
    </Badge>
  }

  const [checkoutItems] = useState(
    shippingCheckoutReport.items?.filter(item =>
      item.scanType != EShippingCheckoutItemScanType.ERROR
    ) || []
  )

  const [errorItems] = useState(
    shippingCheckoutReport.items?.filter(item =>
      item.scanType == EShippingCheckoutItemScanType.ERROR
    ) || []
  )

  return (
    <Fragment>
      <tr>
        <td>{shippingCheckoutReport.fulfilNumber}</td>
        <td>{getStatusPill(shippingCheckoutReport.status)}</td>
        <td>{moment(shippingCheckoutReport.createdAt).format("YYYY-MM-DD HH:mm:ss")}</td>
        <td>{
          shippingCheckoutReport.endedAt
            ? moment.utc(moment(shippingCheckoutReport.endedAt).diff(shippingCheckoutReport.createdAt)).format("HH:mm:ss")
            : null
        }</td>
        <td>{shippingCheckoutReport.admin?.name || shippingCheckoutReport.admin?.username}</td>
        <td>
          <Button
            variant="info-light"
            className="btn-sm"
            onClick={() => setModalShown(true)}
          >
            <span className="ri-eye-line fs-14"></span>
          </Button>
        </td>
      </tr>

      <Modal
        show={modalShown}
        onShow={handleModalShown}
        onHide={() => setModalShown(false)}
        centered
        fullscreen
      >
        <Modal.Header closeButton>
          <Modal.Title>
            <Badge bg={getStatusDisplayType(shippingCheckoutReport.status)}>
              {shippingCheckoutReport.fulfilNumber}
            </Badge>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row>
            <Col lg={4}>
              <Card>
                <Card.Header>
                  <Card.Title>
                    Fulfil Items
                  </Card.Title>
                </Card.Header>
                <Card.Body>
                  {isFulfilFetching &&
                    <div className="text-center">
                      <div className="spinner-border spinner-border-lg" />
                    </div>
                  }
                  <SimpleBar style={{ height: "80vh" }}>
                    {fulfilLines?.map((line, index) => (
                      <Fragment key={index}>
                        <Card
                          id={line.id}
                          className={`my-0 p-2 ${hoveredStatus(line.id)} ${highlightedStatus(line.sku)}`}
                          onMouseEnter={() => handleCardHover(line.id)}
                          onMouseLeave={() => handleCardHover(line.id)}
                          onClick={() => handleItemClick(line.sku)}
                        >
                          <Row>
                            <Col
                              lg={2}
                              className="align-content-center"
                            >
                              <p className="avatar avatar-xl bg-dark-transparent my-auto">
                                {line.variant?.image?.src
                                  ? <img
                                    src={line.variant?.image?.src || ""}
                                    alt="img"
                                    style={{
                                      display: "block",
                                      margin: "0 auto",
                                      width: "100%",
                                      height: "100%",
                                      objectFit: "cover",
                                    }}
                                  />
                                  : "img"
                                }
                              </p>
                            </Col>
                            <Col
                              lg={7}
                              className="align-content-center"
                            >
                              <div>
                                <strong>Product:</strong>
                                {" "}
                                {line.productName}
                              </div>
                              <div>
                                <strong>Variant:</strong>
                                {" "}
                                {line.variantName}
                              </div>
                            </Col>
                            <Col
                              lg={3}
                              className="align-content-center"
                            >
                              <div>
                                <strong>SKU:</strong>
                                {" "}
                                {line.sku}
                              </div>
                              <div>
                                <strong>Barcode:</strong>
                                {" "}
                                {line.barcode ? line.barcode : "No Barcode"}
                              </div>
                              <div>
                                <strong>Quantity:</strong>
                                {" "}
                                {/* {line.count} /  */}
                                {line.quantity}
                              </div>
                            </Col>
                          </Row>
                        </Card>
                      </Fragment>
                    ))}
                  </SimpleBar>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={4}>
              <Card>
                <Card.Header>
                  <Card.Title>
                    Checkout Items
                  </Card.Title>
                </Card.Header>
                <Card.Body>
                  <SimpleBar style={{ height: '80vh' }}>
                    {checkoutItems.map((item, index) => (
                      <Fragment key={index}>
                        <Card
                          id={item.id}
                          className={`my-0 p-2 ${hoveredStatus(item.id)} ${highlightedStatus(item.sku)}`}
                          onMouseEnter={() => handleCardHover(item.id)}
                          onMouseLeave={() => handleCardHover(item.id)}
                          onClick={() => handleItemClick(item.sku)}
                        >
                          <Row>
                            <Col
                              lg={2}
                              className="align-content-center"
                            >
                              <p className="avatar avatar-xl bg-dark-transparent my-auto">
                                {item.variant?.image?.src
                                  ? <img
                                    src={item.variant?.image?.src || ""}
                                    alt="img"
                                    style={{
                                      display: "block",
                                      margin: "0 auto",
                                      width: "100%",
                                      height: "100%",
                                      objectFit: "cover",
                                    }}
                                  />
                                  : "img"
                                }
                              </p>
                            </Col>
                            <Col
                              lg={7}
                              className="align-content-center"
                            >
                              <div>
                                <strong>Product:</strong>
                                {" "}
                                <Link to={`/managements-products/${item.variant?.productId}`}>
                                  <span className="text-decoration-underline">{item.variant?.product?.title}</span>
                                </Link>
                              </div>
                              <div>
                                <strong>Variant:</strong>
                                {" "}
                                {item.variant?.title}
                              </div>
                              <div>
                                <strong>Type:</strong>
                                {" "}
                                {getItemScanTypeBadge(item)}
                                {item.reason &&
                                  <Fragment>
                                    {" | "}
                                    <strong>Reason:</strong>
                                    {" "}
                                    {item.reason}
                                  </Fragment>
                                }
                              </div>
                            </Col>
                            <Col
                              lg={3}
                              className="align-content-center"
                            >
                              <div>
                                <strong>SKU:</strong>
                                {" "}
                                {item.sku}
                              </div>
                              <div>
                                <strong>Barcode:</strong>
                                {" "}
                                {item.barcode}
                              </div>
                              <div>
                                <strong>Time:</strong>
                                {" "}
                                <abbr title={moment(item.createdAt).format("YYYY-MM-DD HH:mm:ss")}>
                                  {
                                    moment.utc(
                                      moment(item.createdAt)
                                        .diff(moment(shippingCheckoutReport.createdAt))
                                    ).format('HH:mm:ss')
                                  }
                                </abbr>
                              </div>
                            </Col>
                          </Row>
                        </Card>
                      </Fragment>
                    ))}
                  </SimpleBar>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={4}>
              <Card>
                <Card.Header>
                  <Card.Title>
                    Error Items
                  </Card.Title>
                </Card.Header>
                <Card.Body>
                  <SimpleBar style={{ height: '80vh' }}>
                    {errorItems.map((item, index) => (
                      <Fragment key={index}>
                        <Card
                          id={item.id}
                          className={`my-0 p-2 ${hoveredStatus(item.id)} ${highlightedStatus(item.sku)}`}
                          onMouseEnter={() => handleCardHover(item.id)}
                          onMouseLeave={() => handleCardHover(item.id)}
                          onClick={() => handleItemClick(item.sku)}
                        >
                          <Row>
                            <Col
                              lg={2}
                              className="align-content-center"
                            >
                              <p className="avatar avatar-xl bg-dark-transparent my-auto">
                                {item.variant?.image?.src
                                  ? <img
                                    src={item.variant?.image?.src || ""}
                                    alt="img"
                                    style={{
                                      display: "block",
                                      margin: "0 auto",
                                      width: "100%",
                                      height: "100%",
                                      objectFit: "cover",
                                    }}
                                  />
                                  : "img"
                                }
                              </p>
                            </Col>
                            <Col
                              lg={7}
                              className="align-content-center"
                            >
                              <div>
                                <strong>Product:</strong>
                                {" "}
                                <Link to={`/managements-products/${item.variant?.productId}`}>
                                  <span className="text-decoration-underline">{item.variant?.product?.title}</span>
                                </Link>
                              </div>
                              <div>
                                <strong>Variant:</strong>
                                {" "}
                                {item.variant?.title}
                              </div>
                              <div>
                                <strong>Error:</strong>
                                {" "}
                                {item.reason}
                              </div>
                            </Col>
                            <Col
                              lg={3}
                              className="align-content-center"
                            >
                              <div>
                                <strong>SKU:</strong>
                                {" "}
                                {item.sku}
                              </div>
                              <div>
                                <strong>Barcode:</strong>
                                {" "}
                                {item.barcode}
                              </div>
                              <div>
                                <strong>Time:</strong>
                                {" "}
                                <abbr title={moment(item.createdAt).format("YYYY-MM-DD HH:mm:ss")}>
                                  {
                                    moment.utc(
                                      moment(item.createdAt)
                                        .diff(moment(shippingCheckoutReport.createdAt))
                                    ).format('HH:mm:ss')
                                  }
                                </abbr>
                              </div>
                            </Col>
                          </Row>
                        </Card>
                      </Fragment>
                    ))}
                  </SimpleBar>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Modal.Body>
      </Modal>
    </Fragment>
  )
}

export default ManagementShippingCheckoutReport;
