import { Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, Modal, Table } from "react-bootstrap";
import "../../../../../../assets/css/event-raffle-modal.css";
import PaginationBar from "../../../../../../components/pagination-bar/pagination-bar";
import {
  useLazyGetStreamUserInteractionsListQuery,
  useLazyGetStreamViewersFullListQuery,
  useLazyGetStreamViewersPaginatedListQuery,
  useSaveStreamRafflePrizeMutation
} from "../../../../../../services/post/stream-post";

interface ISegment {
  id: number;
  // registration: TEventRegistration,
  user: TUser,
  text: string;
  color: string;
}

enum EWheelState {
  PENDING,
  SPINNING,
  SPINNED
}

const StreamRaffleModal = ({
  show,
  setShow,
  streamId,
  setIsLoading,
  // handleRaffleModelClose
}: any) => {
  const [wheelState, setWheelState] = useState<EWheelState>(EWheelState.PENDING);
  const [rotation, setRotation] = useState(0);
  const [winner, setWinner] = useState<ISegment | null>(null);
  const [winners, setWinners] = useState<ISegment[]>([]);
  const [page, setPage] = useState(1);
  const [lastPage, setLastPages] = useState(0);
  const [total, setTotal] = useState(0);
  const [segments, setSegments] = useState<ISegment[]>([]);
  const [segmentAngle, setSegmentAngle] = useState<number>(0);
  const [participants, setParticipants] = useState<any[]>([]);
  const [showParticipantsModal, setShowParticipantsModal] = useState<boolean>(false);
  const [showWinnersModal, setShowWinnersModal] = useState(false)
  const [showResultModal, setShowResultModal] = useState<boolean>(false);
  const [isGettingStreamInteraction, setIsGettingStreamInteraction] = useState(false)

  const [getAllViewers] = useLazyGetStreamViewersFullListQuery()
  const [getPaginatedViewers] = useLazyGetStreamViewersPaginatedListQuery()
  const [getStreamUserInteraction] = useLazyGetStreamUserInteractionsListQuery()
  const [saveRafflePrize] = useSaveStreamRafflePrizeMutation()

  const WHEEL_SIZE = 1000;
  const RADIUS = WHEEL_SIZE / 2 - 30;
  const CENTER_POINT_RADIUS = 60;
  const SPIN_DURATION = 15

  const colors = [
    '#E14643',
    '#4CB153',
    '#EA913E',
    '#49A7D9',
    '#F4EA54',
    '#343C89',
    '#8EC051',
    '#E35D3B',
    '#2D904C',
    '#EFAD52',
    '#3873B2',
    '#D4DC4A',
  ];

  useEffect(() => {
    if (!show) return;
    loadAllCheckedInRegistrations();
    loadCheckedInRegistrationsWithPagination();
  }, [show]);

  const [refresh, setRefresh] = useState(false)
  useEffect(() => {
    loadCheckedInRegistrationsWithPagination();
  }, [page, refresh]);

  const refreshStreamUserInteractions = () => {
    setIsGettingStreamInteraction(true)
    getStreamUserInteraction({ streamId })
      .unwrap()
      .then(res => handleSetSegments(res))
      .catch((error) =>
        console.error(error))
      .finally(() => {
        setRefresh(!refresh)
        setIsGettingStreamInteraction(false)
      })
  }

  const loadAllCheckedInRegistrations = () => {
    setIsLoading(true);

    getAllViewers({ streamId })
      .unwrap()
      .then((res) => {
        handleSetSegments(res);
      })
      .catch((error) => {
        console.error(error);
      })
      .finally(() => { setIsLoading(false) });
  }

  const loadCheckedInRegistrationsWithPagination = () => {
    getPaginatedViewers({
      streamId,
      page,
    })
      .unwrap()
      .then((res) => {
        setLastPages(res?.meta?.lastPage);
        setTotal(res?.meta?.total)
        setParticipants(res.data || []);
      })
      .catch((error) => {
        console.error(error);
      })
  }

  const handleSetSegments = (registrations: any[]) => {
    const joiners = registrations.filter(regis => !regis.raffleWinner)
    const newSegments: ISegment[] = joiners.map((reg: any) => ({
      // id: reg.raffle?.luckyNumber || 0,
      id: reg.id || 0,
      // registration: reg,
      user: reg.user,
      // text: `${reg.raffle?.luckyNumber}`,
      text: `${reg.luckyNumber}`,
      // color: colors[(reg.raffle?.luckyNumber || 0) % colors.length]
      color: colors[(reg.luckyNumber || 0) % colors.length]
    }));
    setSegments(newSegments);
    setSegmentAngle(360 / newSegments.length);
    setWinners(registrations.filter(regis => regis.raffleWinner).sort((a, b) => a.luckyNumber - b.luckyNumber))
  }

  const handleSpecialPrizeButtonPress = () => {
    spinWheel();
  }

  const spinWheel = () => {
    if (wheelState !== EWheelState.PENDING) return;

    setWheelState(EWheelState.SPINNING);
    setWinner(null);

    // Generate random rotation (multiple full rotations + random final position)
    const minSpins = 25;
    const maxSpins = 50;
    const spins = Math.random() * (maxSpins - minSpins) + minSpins;
    const finalRotation = spins * 360 + Math.random() * 360;

    setRotation(prev => prev + finalRotation);

    // Calculate winner after spin completes
    setTimeout(() => {
      const normalizedRotation = ((270 - (finalRotation % 360)) + 360) % 360;
      const winningIndex = Math.floor(normalizedRotation / segmentAngle);
      const winner = segments[winningIndex];

      setWinner(winner);
      setWheelState(EWheelState.SPINNED);
      setShowResultModal(true);
    }, SPIN_DURATION * 950); // Match the CSS animation duration
  };

  const saveSpinResult = () => {
    setIsLoading(true);

    saveRafflePrize({
      streamId,
      interactionId: winner?.id,
    })
      .unwrap()
      .then((res) => {
        updateParticipants(res);
        resetWheel();
      })
      .catch((error) => {
        console.error(error);
      })
      .finally(() => {
        setIsLoading(false);
      });

  }

  const updateParticipants = (winnerPerson: any | null) => {
    if (!winnerPerson) { return }

    const newSegments = segments.filter(sgm => sgm.id !== winnerPerson.id);

    setWinners([...winners, winnerPerson].sort((a, b) => a.luckyNumber - b.luckyNumber))
    setSegments(newSegments);
    setSegmentAngle(360 / newSegments.length);
    setParticipants(participants.map(parti => parti.id != winnerPerson.id ? parti : winnerPerson))
  }

  const resetWheel = () => {
    setRotation(0);
    setWinner(null);
    setWheelState(EWheelState.PENDING);
  };

  const getFormattedWinnerName = (winnerName?: string) => {
    if (!winnerName)
      return null;
    return winnerName.length > 50 ? winnerName.substring(0, 50) + '...' : winnerName;
  }

  return (
    <Fragment>
      <Modal
        show={show}
        onHide={() => {
          setShow(false);
          // handleRaffleModelClose();
        }}
        fullscreen={true}
        className="raffle-modal"
        keyboard={false}
      >
        <Modal.Header>
          <div className="d-flex justify-content-between w-100 align-items-center">
            <Button
              variant="light"
              onClick={() => {
                setShow(false);
                // handleRaffleModelClose();
              }}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '1.5rem',
                lineHeight: '1',
                color: '#FFF',
                cursor: 'pointer'
              }}>
              &times;
            </Button>

            <div>
              <Button
                className="me-2"
                onClick={refreshStreamUserInteractions}
              >
                {
                  isGettingStreamInteraction
                    ? <i className="spinner-border spinner-border-sm mx-3" />
                    : "Refresh"
                }
              </Button>
              {winners && winners.length > 0 &&
                <Button
                  className="me-2"
                  variant="warning"
                  onClick={() => {
                    setShowWinnersModal(true);
                  }}>
                  Winners
                </Button>
              }
              <Button
                variant="success"
                onClick={() => {
                  setShowParticipantsModal(true);
                }}>
                Participants
              </Button>
            </div>

            <div></div>
          </div>
        </Modal.Header>

        <Modal.Body>
          <div className="raffle-container">
            {/* Wheel Container */}
            <div className="wheel-container">
              {/* Pointer */}
              <div className="wheel-pointer"></div>

              {/* Wheel */}
              <div>
                <svg
                  width={`${WHEEL_SIZE}`}
                  height={`${WHEEL_SIZE}`}
                  className="wheel-svg"
                  style={{
                    transform: `rotate(${rotation}deg)`,
                    transition: wheelState === EWheelState.SPINNING ? `transform ${SPIN_DURATION}s cubic-bezier(0.23, 1, 0.32, 1)` : 'none'
                  }}
                >
                  {segments.map((prize, index) => {
                    const startAngle = index * segmentAngle;
                    const endAngle = (index + 1) * segmentAngle;
                    const startAngleRad = (startAngle * Math.PI) / 180;
                    const endAngleRad = (endAngle * Math.PI) / 180;

                    const x1 = WHEEL_SIZE / 2 + RADIUS * Math.cos(startAngleRad);
                    const y1 = WHEEL_SIZE / 2 + RADIUS * Math.sin(startAngleRad);
                    const x2 = WHEEL_SIZE / 2 + RADIUS * Math.cos(endAngleRad);
                    const y2 = WHEEL_SIZE / 2 + RADIUS * Math.sin(endAngleRad);

                    const largeArcFlag = segmentAngle > 180 ? 1 : 0;

                    const pathData = [
                      `M ${WHEEL_SIZE / 2} ${WHEEL_SIZE / 2}`,
                      `L ${x1} ${y1}`,
                      `A ${RADIUS} ${RADIUS} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                      `Z`
                    ].join(' ');

                    // Calculate text position
                    const textAngle = startAngle + segmentAngle / 2;
                    const textAngleRad = (textAngle * Math.PI) / 180;
                    const textX = WHEEL_SIZE / 2 + (RADIUS - 30) * Math.cos(textAngleRad);
                    const textY = WHEEL_SIZE / 2 + (RADIUS - 30) * Math.sin(textAngleRad);

                    return (
                      <g key={prize.id}>
                        <path
                          d={pathData}
                          fill={prize.color}
                          stroke="#fff"
                          strokeWidth="2"
                        />
                        <text
                          x={textX}
                          y={textY}
                          fill="#fff"
                          fontSize="14"
                          fontWeight="bold"
                          textAnchor="middle"
                          dominantBaseline="middle"
                          transform={`rotate(${textAngle + 90}, ${textX}, ${textY})`}
                        >
                          {prize.text}
                        </text>
                      </g>
                    );
                  })}

                  {/* Center circle */}
                  <circle
                    cx={`${WHEEL_SIZE / 2}`}
                    cy={`${WHEEL_SIZE / 2}`}
                    r={CENTER_POINT_RADIUS}
                    fill="#2D3748"
                    stroke="#fff"
                    strokeWidth="4"
                  />
                  <defs>
                    <clipPath id="circleClip">
                      <circle cx={`${WHEEL_SIZE / 2}`} cy={`${WHEEL_SIZE / 2}`} r={CENTER_POINT_RADIUS} />
                    </clipPath>
                  </defs>

                  <image
                    href="https://s3.amazonaws.com/files.zurno.com/2025/05/18/1747574823298-419c8b5c-4454-4088-b5c2-7aa6d9ebbefe.webp"
                    x={`${(WHEEL_SIZE / 2) - CENTER_POINT_RADIUS + 1}`}
                    y={`${(WHEEL_SIZE / 2) - CENTER_POINT_RADIUS - 1}`}
                    width={`${CENTER_POINT_RADIUS * 2}px`}
                    height={`${CENTER_POINT_RADIUS * 2}px`}
                    clipPath="url(#circleClip)"
                  />
                </svg>
              </div>
            </div>

            {/* Controls */}
            <div className="controls">
              {(wheelState === EWheelState.PENDING) && (<button
                onClick={handleSpecialPrizeButtonPress}
                className="spin-button"
                disabled={!participants || participants.length == 0}
              >
                <span className="fs-40">
                  🎖️ Special Prize
                </span>
              </button>)}

              {(wheelState === EWheelState.SPINNING) && (<button
                onClick={() => { }}
                disabled={true}
                className="spin-button"
              >
                <span className="fs-40">
                  🎡 Spinning...
                </span>
              </button>)}

              {(wheelState === EWheelState.SPINNED) && (
                <button
                  onClick={saveSpinResult}
                  className="spin-button"
                >
                  <span className="fs-40">
                    📥 Save
                  </span>
                </button>
              )}

              {(wheelState === EWheelState.SPINNED) && (
                <button
                  onClick={resetWheel}
                  className="reset-button"
                >
                  <span className="fs-40">
                    ❌ Drop
                  </span>
                </button>
              )}

              {(wheelState === EWheelState.SPINNED) && (
                <button
                  onClick={() => { setShowResultModal(true) }}
                  className="reset-button"
                >
                  <span className="fs-40">
                    📊 Result
                  </span>
                </button>
              )}
            </div>
          </div>
        </Modal.Body>
      </Modal>

      <Modal
        show={showParticipantsModal}
        onHide={() => { setShowParticipantsModal(false) }}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>
            <span className="fs-40 fw-bold">
              Participants : {total}
            </span>
          </Modal.Title>
        </Modal.Header>

        <Modal.Body>
          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
              <tr>
                <th>
                  <span className="fs-30 fw-bold">
                    #
                  </span>
                </th>
                <th>
                  <span className="fs-30 fw-bold">
                    Name
                  </span>
                </th>
              </tr>
            </thead>
            <tbody>
              {
                participants.map((participant: any) => (
                  <tr key={participant.id} className={participant.raffleWinner ? "table-warning" : ""}>
                    <td>
                      <span className="fs-30">
                        {participant.luckyNumber}
                      </span>
                    </td>
                    <td>
                      <span className="fs-30">
                        {participant.user?.fullname}
                      </span>
                    </td>
                  </tr>
                ))
              }
            </tbody>
          </Table>

          <PaginationBar
            page={page}
            setPage={setPage}
            lastPage={lastPage}
          />
        </Modal.Body>
      </Modal>

      <Modal
        show={showWinnersModal}
        onHide={() => { setShowWinnersModal(false) }}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>
            <span className="fs-40 fw-bold">
              Winners : {winners.length}
            </span>
          </Modal.Title>
        </Modal.Header>

        <Modal.Body>
          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
              <tr>
                <th>

                  <span className="fs-30 fw-bold">
                    #
                  </span>
                </th>
                <th>
                  <span className="fs-30 fw-bold">
                    Name
                  </span>
                </th>
              </tr>
            </thead>
            <tbody>
              {
                winners.map((participant: any) => (
                  <tr key={participant.id} className={participant.raffleWinner ? "table-warning" : ""}>
                    <td>
                      <span className="fs-30">
                        {participant.luckyNumber}
                      </span>
                    </td>
                    <td>
                      <span className="fs-30">
                        {participant.user?.fullname}
                      </span>
                    </td>
                  </tr>
                ))
              }
            </tbody>
          </Table>
        </Modal.Body>
      </Modal>

      <Modal
        show={showResultModal}
        onHide={() => { setShowResultModal(false) }}
        centered
        size='lg'
      >
        <Modal.Header closeButton className="py-0"></Modal.Header>
        <Modal.Body>
          {/* Winner Display */}
          {winner && (
            <div className="winner-display">
              <p className="fw-bold">
                <span className="fs-40">
                  🎉 Congratulations! 🎉
                </span>
              </p>
              <p>
                <span className="fs-40">
                  Won Number: <span className="winner-prize">{winner?.text}</span>
                </span>
              </p>
              <p>
                <span className="fs-40">
                  <span className="winner-prize">{getFormattedWinnerName(winner?.user.fullname)}</span>
                </span>
              </p>
              <p>&nbsp;</p>
            </div>
          )}
        </Modal.Body>
      </Modal>
    </Fragment >
  )
}

export default StreamRaffleModal;