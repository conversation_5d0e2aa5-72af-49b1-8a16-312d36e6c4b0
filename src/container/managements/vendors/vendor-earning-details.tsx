import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, Modal, Row, Table } from "react-bootstrap";
import { useParams } from "react-router-dom";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { getAllErrorMessages } from "../../../utils/errors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { format } from "date-fns";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import CommissionSyncButton from "../../../components/buttons/commission-sync-button";
import { ErrorType } from "../../../utils/error_type";
import { useLazyGetVendorEarningByIdQuery, useUpdateVendorEarningAmountMutation } from "../../../services/vendors";

interface VendorEarningDetailProps { }

const VendorEarningDetail: FC<VendorEarningDetailProps> = () => {
  const { id } = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<ErrorType>({});
  const [earning, setEarning] = useState<Partial<TVendorEarning>>({});
  const [adjustmentFormData, setAdjustmentFormData] = useState({
    adjustedAmount: 0,
    adjustedReason: ''
  })
  const [showAdjustmentModal, setShowAdjustmentModal] = useState<boolean>(false)

  const [getEarningById] = useLazyGetVendorEarningByIdQuery();
  const [updateCommissionAmount] = useUpdateVendorEarningAmountMutation();

  const formattedCommissionStatus = (status: EApprovalStatus | undefined) => {
    if (!status) return (<span className="badge bg-warning-transparent">Unknown</span>);

    if (status == EApprovalStatus.PENDING)
      return (<span className="badge bg-warning-transparent">Pending</span>)
    else if (status == EApprovalStatus.APPROVED)
      return (<span className="badge bg-success-transparent">Approved</span>)
    else
      return (<span className="badge bg-danger-transparent">Rejected</span>)
  }

  const loadCommission = () => {
    if (!id) return;
    setIsLoading(true);
    setErr({});

    getEarningById(id)
      .unwrap()
      .then((res) => {
        setEarning(res);
        setAdjustmentFormData({
          adjustedAmount: res.adjustedAmount ?? 0,
          adjustedReason: res.adjustedReason ?? '',
        })
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => setIsLoading(false));
  }

  const adjustmentFieldChange = (e) => {
    const { name, value } = e.target;

    if (name === 'adjustedAmount' && (isNaN(value) || Number(value) < 0))
      return

    setAdjustmentFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleUpdateCommissionAmount = () => {
    if (!earning || !earning.id) return
    setIsLoading(true)

    updateCommissionAmount({
      id: earning.id,
      adjustedAmount: adjustmentFormData.adjustedAmount,
      adjustedReason: adjustmentFormData.adjustedReason
    })
      .unwrap()
      .then((res) => {
        setEarning({
          ...earning,
          finalAmount: res.finalAmount,
          adjustedAmount: res.adjustedAmount,
          adjustedReason: res.adjustedReason
        });
        setErr({});
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      })
  }

  // const onFinishedCommissionApproval = ({ success, updatedCommission }: { success: boolean; updatedCommission: TVendorEarning; }) => {
  //   if (success) {
  //     setCommission(updatedCommission);
  //     setAdjustmentFormData({
  //       adjustedAmount: updatedCommission.adjustedAmount,
  //       adjustedReason: updatedCommission.adjustedReason
  //     })
  //     setErr({});
  //   }
  // }

  useEffect(() => {
    loadCommission();
  }, [id]);

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Row>

        <Col xl={7}>
          <Card className="custom-card" style={{ height: '330px' }}>
            <Card.Header>
              <CardHeaderWithBack
                title='Commission Details'
                route=''
              />
              {formattedCommissionStatus(earning.status)}

              {/* <div className="px-4 justify-content-end">
                <CommissionApproveButton
                  commission={commission}
                  setIsLoading={setIsLoading}
                  onFinished={onFinishedCommissionApproval}
                />

                <CommissionRejectButton
                  commission={commission}
                  setIsLoading={setIsLoading}
                  onFinished={onFinishedCommissionApproval}
                />
              </div> */}
            </Card.Header>

            <Card.Body>
              {err?.messages?.map((message: string, index: number) => (
                <Alert key={index} variant="danger">
                  {message}
                </Alert>
              ))}

              <ul className="list-unstyled order-details-list">
                {[
                  [
                    'Company:',
                    earning.vendor?.companyName,
                  ],
                  [
                    'Created At:',
                    earning.createdAt ? `${format(earning.createdAt, 'EEE, MMM dd, yyyy, hh:mm aa')}` : '',
                  ],
                  [
                    'Total Commission Amount:',
                    `$${earning.finalAmount?.toFixed(2)}`,
                  ],
                ].map((field) => (
                  <li key={Math.random()}>
                    <span className="me-2 text-default fw-semibold">
                      {field[0]}
                    </span>
                    <span className="fs-14 text-muted">
                      {field[1]}
                    </span>
                  </li>
                ))}
              </ul>
            </Card.Body>
          </Card>
        </Col>

        <Col xl={5}>
          <Card className="custom-card" style={{ height: '330px' }}>
            <Card.Header>
              <Card.Title>Order</Card.Title>
            </Card.Header>
            <Card.Body>
              <ul className="list-unstyled order-details-list">
                {[
                  [
                    'Name:',
                    `${earning.order?.name}`,
                  ],
                  [
                    'ID:',
                    `${earning.order?.id}`,
                  ],
                  [
                    'Shopify ID:',
                    `${earning.order?.shopifyId}`,
                  ],
                  [
                    'Financial Status:',
                    `${earning.order?.financialStatus}`,
                  ],
                  [
                    'Total Price:',
                    `$${earning.order?.totalPrice}`,
                  ],
                  [
                    'Total Discount:',
                    `$${earning.order?.totalDiscounts}`,
                  ],
                  [
                    'Customer:',
                    `${earning.order?.user?.firstName} ${earning.order?.user?.lastName}`,
                  ],
                ].map((field) => (
                  <li key={Math.random()}>
                    <span className="me-2 text-default fw-semibold">
                      {field[0]}
                    </span>
                    <span className="fs-14 text-muted">
                      {field[1]}
                    </span>
                  </li>
                ))}
              </ul>
            </Card.Body>
          </Card>
        </Col>

      </Row>

      <Card className="custom-card">
        <Card.Header>
          <Card.Title>Details</Card.Title>
          <div className="px-4 justify-content-end">
            <CommissionSyncButton
              commissionId={earning?.id || ''}
              setIsLoading={setIsLoading}
              loadData={loadCommission}
            />
            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION) || (earning.status == EApprovalStatus.REJECTED && earning.earnedAmount === 0)}
              variant="warning"
              className="btn btn-warning ms-2"
              onClick={() => { setShowAdjustmentModal(true) }}
            >
              Adjust
            </Button>
          </div>
        </Card.Header>
        <Card.Body>
          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
              <tr>
                <th>&nbsp;</th>
                <th>&nbsp;</th>
                <th>A</th>
                <th>B</th>
                <th>C = A * B</th>
                <th>D</th>
                <th>E = C - D</th>
                <th>F</th>
                <th>G</th>
                <th>H = E * (100% - F) - G</th>
              </tr>
              <tr>
                <th>Item</th>
                <th>SKU</th>
                <th>Quantity</th>
                <th>Unit Price ($)</th>
                <th>Item Subtotal ($)</th>
                <th>Item Discount ($)</th>
                <th>Item Total After Discounted ($)</th>
                <th>Commission Rate</th>
                <th>Fixed Commission Amount ($)</th>
                <th>Commission Amount ($)</th>
              </tr>
            </thead>
            <tbody>
              {
                earning.earningDetails?.map((earningDetail: TVendorEarningDetail) => (
                  <Fragment key={earningDetail.id}>
                    <tr>
                      <td>{earningDetail.orderDetail.title}</td>
                      <td>{earningDetail.orderDetail.sku}</td>
                      <td>{earningDetail.orderDetail.currentQuantity}</td>
                      <td>{earningDetail.orderDetail.price.toFixed(2)}</td>
                      <td>{earningDetail ? (earningDetail.orderDetail.price * earningDetail.orderDetail.currentQuantity).toFixed(2) : '0'}</td>
                      <td>{earningDetail.orderDetail.discount.toFixed(2)}</td>
                      <td>{earningDetail ? (earningDetail.orderDetail.price * earningDetail.orderDetail.currentQuantity - earningDetail.orderDetail.discount).toFixed(2) : '0'}</td>
                      <td>{`${earningDetail.commissionRate * 100}%`}</td>
                      <td>{earningDetail.fixedCommissionAmount.toFixed(2)}</td>
                      <td>{earningDetail.earnedAmount.toFixed(2)}</td>
                    </tr>
                  </Fragment>
                ))
              }
              <tr>
                <td colSpan={9}>Total</td>
                <td>{earning.earnedAmount?.toFixed(2) ?? '0'}</td>
              </tr>
              {
                (earning.adjustedAmount !== null && earning.adjustedAmount !== undefined && earning.adjustedAmount > 0) &&
                (<tr>
                  <td colSpan={8}>Adjusted Total (Reason: {earning.adjustedReason}) </td>
                  <td>{earning.adjustedAmount.toFixed(2)}</td>
                </tr>
                )
              }
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      <AdjustmentDialog
        show={showAdjustmentModal}
        setShow={setShowAdjustmentModal}
        formData={adjustmentFormData}
        onFieldChange={adjustmentFieldChange}
        handleConfirm={handleUpdateCommissionAmount}
      />
    </Fragment>
  );
}

const AdjustmentDialog = ({
  show,
  setShow,
  formData,
  onFieldChange,
  handleConfirm
}) => {
  const onCancel = () => {
    setShow(false)
  }

  const onSubmit = (e) => {
    e.preventDefault()

    setShow(false)
    handleConfirm()
  }

  return (
    <Fragment>
      <Modal show={show} onHide={onCancel}>
        <Form onSubmit={onSubmit}>

          <Modal.Header closeButton>
            <Modal.Title>Adjust Commission Amount</Modal.Title>
          </Modal.Header>

          <Modal.Body>
            <Form.Group className="mb-4">
              <Form.Label>Adjusted Amount</Form.Label>
              <Form.Control
                type="text"
                name="adjustedAmount"
                value={formData.adjustedAmount}
                onChange={onFieldChange}
              />
            </Form.Group>

            <Form.Group className="mb-4">
              <Form.Label>Adjusted Reason</Form.Label>
              <Form.Control
                type="text"
                name="adjustedReason"
                value={formData.adjustedReason}
                onChange={onFieldChange}
              />
            </Form.Group>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="secondary" onClick={onCancel}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Confirm
            </Button>
          </Modal.Footer>

        </Form>
      </Modal>
    </Fragment>
  )
}

export default VendorEarningDetail;