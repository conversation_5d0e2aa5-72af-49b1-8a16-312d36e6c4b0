import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Table } from "react-bootstrap";
import { useLazyGetVendorPaymentsQuery } from "../../../services/vendors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { getAllErrorMessages } from "../../../utils/errors";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import { format } from "date-fns";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import VendorPaymentDialog from "../../../components/dialog/vendor-payment-dialog";

interface VendorPayoutProps {
  vendor: TVendor;
}

const VendorPayout: FC<VendorPayoutProps> = ({ vendor }) => {
  const [payments, setPayments] = useState<TVendorPayment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [showPaymentModal, setShowPaymentModal] = useState<boolean>(false);

  const [getVendorPayments] = useLazyGetVendorPaymentsQuery();

  const loadVendorPayments = (vendor: TVendor) => {
    setIsLoading(true);
    setErr({});
    getVendorPayments({ vendorId: vendor.id, params: {} })
      .unwrap()
      .then((res) => {
        setTotalPages(res?.meta?.lastPage);
        setPayments(res.data || []);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const handlePaymentModalClose = (success) => {
    setShowPaymentModal(false);
    if (success) {
      loadVendorPayments(vendor);
    }
  }

  useEffect(() => {
    loadVendorPayments(vendor);
  }, []);

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <Card.Title>Payouts</Card.Title>
          <div className="px-4 justify-content-end">
            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.VENDOR)}
              variant="primary-light m-2"
              onClick={() => { setShowPaymentModal(true) }}
            >
              Pay <i className="bi bi-currency-dollar" />
            </Button>
          </div>
        </Card.Header>

        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
              <tr>
                <th>Date Time</th>
                <th>Payment Method</th>
                <th>Amount ($)</th>
              </tr>
            </thead>
            <tbody>
              {
                payments.map(pm => (
                  <tr>
                    <td>{format(pm.createdAt, 'EEE, MMM dd, yyyy, hh:mm aa')}</td>
                    <td>{pm.paymentMethod?.paymentTypeName}</td>
                    <td>{pm.amount.toFixed(2)}</td>
                  </tr>
                ))
              }
            </tbody>
          </Table>
        </Card.Body>

        <Card.Footer>
          <PaginationBar
            page={page}
            setPage={setPage}
            lastPage={totalPages}
          />
        </Card.Footer>
      </Card>

      <VendorPaymentDialog
        vendor={vendor}
        show={showPaymentModal}
        setIsLoading={setIsLoading}
        handleClose={handlePaymentModalClose} />
    </Fragment>
  );
}

export default VendorPayout;