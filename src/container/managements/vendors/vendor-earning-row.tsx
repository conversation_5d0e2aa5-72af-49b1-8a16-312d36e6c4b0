import { FC, Fragment, useEffect } from "react";
import { But<PERSON> } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { hasPermission } from "../../../utils/authorization";
import { useState } from "react";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import { format } from "date-fns";
import VendorEarningApproveButton from "./vendor-earning-approve-button";
import VendorEarningRejectButton from "./vendor-earning-reject-button";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";

interface VendorEarningRowProps {
  inputCommission: TVendorEarning;
  setErr: any
}

const VendorEarningRow: FC<VendorEarningRowProps> = ({ inputCommission, setErr }) => {
  const [earning, setCommission] = useState(inputCommission);
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    setCommission(inputCommission);
  }, [inputCommission]);

  const onFinishedApproveEarning = ({ success, updatedCommission }: { success: boolean; updatedCommission: TVendorEarning; }) => {
    if (success) {
      setCommission(updatedCommission);
      setErr({});
    }
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <tr>
        <td>{earning.vendor?.companyName}</td>
        <td>{format(earning.createdAt, 'EEE, MMM dd, yyyy, hh:mm aa')}</td>
        <td>{earning.finalAmount}</td>

        <td style={{ textAlign: "center" }}>
          {
            earning.status == EApprovalStatus.PENDING ? (
              <span className="badge bg-warning-transparent">Pending</span>
            ) : earning.status == EApprovalStatus.APPROVED ? (
              <span className="badge bg-success-transparent">Approve</span>
            ) : (
              <span className="badge bg-danger-transparent">Rejected</span>
            )
          }
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          <Button
            hidden={!hasPermission(ACTION.READ, RESOURCE.AFFILIATION)}
            variant="primary-light"
            className="btn btn-primary-light btn-sm ms-2"
            onClick={() => { navigate(earning.id) }}
          >
            View
          </Button>
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          <VendorEarningApproveButton
            earning={earning}
            setIsLoading={setIsLoading}
            onFinished={onFinishedApproveEarning}
          />
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          <VendorEarningRejectButton
            earning={earning}
            setIsLoading={setIsLoading}
            onFinished={onFinishedApproveEarning}
          />
        </td>
      </tr>
    </Fragment >
  );
}

export default VendorEarningRow;