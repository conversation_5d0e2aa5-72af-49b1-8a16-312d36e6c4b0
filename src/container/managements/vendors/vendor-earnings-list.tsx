import { Fragment, useEffect, useState } from "react";
import { Al<PERSON>, Card, Table } from "react-bootstrap";
import Card<PERSON><PERSON>erW<PERSON>Back from "../../../components/table-title/card-header-with-back";
import { useLazyGetVendorEarningsQuery } from "../../../services/vendors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { getAllErrorMessages } from "../../../utils/errors";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import VendorEarningRow from "./vendor-earning-row";

export default function VendorEarningsList() {
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [commissions, setCommissions] = useState<TVendorEarning[]>([]);

  const [getCommissionsList] = useLazyGetVendorEarningsQuery();

  useEffect(() => {
    loadVendorsList({ page });
  }, [page]);

  const loadVendorsList = (query: TQueryAPI) => {
    setIsLoading(true);
    getCommissionsList(query)
      .unwrap()
      .then((res) => {
        setTotalPages(res?.meta?.lastPage);
        setCommissions(res.data || []);
        setErr({});
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack
            title='Earnings'
            route=''
          />
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
              <tr>
                <th>Vendor</th>
                <th>Created at</th>
                <th>Amount ($)</th>
                <th className="w-5" style={{ textAlign: "center" }}>Status</th>
                <th colSpan={3} style={{ textAlign: "center" }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {
                commissions.map((commission) => (
                  < VendorEarningRow
                    inputCommission={commission}
                    setErr={setErr}
                  />
                ))
              }
            </tbody>
          </Table>

          <PaginationBar
            page={page}
            setPage={setPage}
            lastPage={totalPages}
          />
        </Card.Body>
      </Card>
    </Fragment>
  );
}