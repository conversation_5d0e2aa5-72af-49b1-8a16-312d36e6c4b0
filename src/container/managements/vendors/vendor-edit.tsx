import { FC, Fragment, useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Card, Button, Form, Alert } from "react-bootstrap";
import { useLazyGetVendorByIdQuery, useUpdateVendorMutation } from "../../../services/vendors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { getAllErrorMessages } from "../../../utils/errors";

const VendorEdit: FC = () => {
  const { id: vendorId } = useParams<{ id: string }>();

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [vendor, setVendor] = useState<TVendor>();

  const navigate = useNavigate();
  const [getVendor] = useLazyGetVendorByIdQuery();
  const [updateVendor] = useUpdateVendorMutation();

  const [form, setForm] = useState({
    companyName: "",
    brandName: "",
    website: "",
    contactName: "",
    phone: "",
    email: "",
    address1: "",
    address2: "",
    city: "",
    state: "",
    country: "",
    zipCode: "",
    ein: "",
    commissionRate: 0,
    fixedCommissionAmount: 0,
  });

  const loadVendor = (vendorId: string) => {
    setIsLoading(true);
    setErr({});
    getVendor(vendorId)
      .unwrap()
      .then(setVendor)
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!vendorId) return;

    setIsLoading(true);
    setErr({});
    updateVendor({ id: vendorId, data: form })
      .unwrap()
      .then(() => {
        navigate(-1);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    if (vendorId) {
      loadVendor(vendorId);
    }
  }, []);

  useEffect(() => {
    if (vendor) {
      setForm({
        companyName: vendor.companyName || "",
        brandName: vendor.brandName || "",
        website: vendor.website || "",
        contactName: vendor.contactName || "",
        phone: vendor.phone || "",
        email: vendor.email || "",
        address1: vendor.address1 || "",
        address2: vendor.address2 || "",
        city: vendor.city || "",
        state: vendor.state || "",
        country: vendor.country || "",
        zipCode: vendor.zipCode || "",
        ein: vendor.ein || "",
        commissionRate: vendor.commissionRate || 0,
        fixedCommissionAmount: vendor.fixedCommissionAmount || 0,
      });
    }
  }, [vendor]);

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack title="Edit Vendor" route="" />
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Company Name</Form.Label>
              <Form.Control
                name="companyName"
                value={form.companyName}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Brand Name</Form.Label>
              <Form.Control
                name="brandName"
                value={form.brandName}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Website</Form.Label>
              <Form.Control
                name="website"
                value={form.website}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Contact Name</Form.Label>
              <Form.Control
                name="contactName"
                value={form.contactName}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Phone</Form.Label>
              <Form.Control
                name="phone"
                value={form.phone}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Email</Form.Label>
              <Form.Control
                name="email"
                value={form.email}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Address 1</Form.Label>
              <Form.Control
                name="address1"
                value={form.address1}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Address 2</Form.Label>
              <Form.Control
                name="address2"
                value={form.address2}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>City</Form.Label>
              <Form.Control
                name="city"
                value={form.city}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>State</Form.Label>
              <Form.Control
                name="city"
                value={form.state}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Country</Form.Label>
              <Form.Control
                name="country"
                value={form.country}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Zip Code</Form.Label>
              <Form.Control
                name="country"
                value={form.zipCode}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>EIN</Form.Label>
              <Form.Control
                name="ein"
                value={form.ein}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Commission Rate</Form.Label>
              <Form.Control
                name="commissionRate"
                value={form.commissionRate}
                onChange={handleChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Fixed Commission Amount</Form.Label>
              <Form.Control
                name="fixedCommissionAmount"
                value={form.fixedCommissionAmount}
                onChange={handleChange}
              />
            </Form.Group>
            <Button type="submit" variant="primary" disabled={isLoading}>
              {isLoading ? "Saving..." : "Save"}
            </Button>
            <Button variant="secondary" className="ms-2" onClick={() => navigate(-1)}>
              Cancel
            </Button>
          </Form>
        </Card.Body>
      </Card>
    </Fragment>
  );
};

export default VendorEdit;