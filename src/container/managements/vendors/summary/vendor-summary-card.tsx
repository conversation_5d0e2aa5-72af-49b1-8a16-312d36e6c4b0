import { FC, Fragment, useEffect, useState } from "react";
import { Card, Col, Row } from "react-bootstrap";
import { Link } from "react-router-dom";
import VendorRevenueCard from "./vendor-revenue-card";
import { useLazyGetVendorStatsQuery } from "../../../../services/vendors";
import { getAllErrorMessages } from "../../../../utils/errors";

interface VendorSummaryCardProps {
  vendorId: string;
  setIsLoading: (loading: boolean) => void;
  setErr: any;
}

interface CardsData {
  id: string;
  title: string;
  text1: string;
  text2: string;
  icon1: string;
  icon2: string;
  color1: string;
  color2: string;
}

const VendorSummaryCard: FC<VendorSummaryCardProps> = ({ vendorId, setIsLoading, setErr }) => {
  const [stats, setStats] = useState<TVendorStats>();

  const [getVendorStats] = useLazyGetVendorStatsQuery();

  const loadVendorStats = (vendorId: string) => {
    setIsLoading(true);
    getVendorStats(vendorId)
      .unwrap()
      .then(setStats)
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const cardsData1: CardsData[] = [
    {
      id: "1",
      title: "Total Sales",
      text1: "$18,645",
      text2: "+24%",
      icon1: "bi bi-cart-check fs-20",
      icon2: "down",
      color1: "primary",
      color2: "primary",
    },
  ];

  const cardsData2: CardsData[] = [
    {
      id: "3",
      title: "Total Products",
      text1: "26,231",
      text2: "+06%",
      icon1: "bi bi-handbag fs-20",
      icon2: "down",
      color1: "info",
      color2: "danger",
    },
    {
      id: "4",
      title: "Total Expenses",
      text1: "$73,579",
      text2: "+06%",
      icon1: "bi bi-currency-dollar fs-20",
      icon2: "up",
      color1: "warning",
      color2: "success",
    },
    {
      id: "5",
      title: "Active Subscribers",
      text1: "1,468",
      text2: "+16% ",
      icon1: "bi bi-bell fs-20",
      icon2: "down",
      color1: "danger",
      color2: "danger",
    },
  ];

  useEffect(() => {
    if (vendorId) {
      loadVendorStats(vendorId);
    }
  }, [vendorId]);

  const getMockCard = (cardData: CardsData) => {
    return (
      <div
        className="col card-background flex-fill"
        key={Math.random()}
      >
        <div className="card custom-card">
          <Card.Body>
            <div className="d-flex">
              <div>
                <p className="fw-medium mb-1 text-muted">{cardData.title}</p>
                <h3 className="mb-0">{cardData.text1}</h3>
              </div>
              <div
                className={`avatar avatar-md br-4 bg-${cardData.color1}-transparent ms-auto`}
              >
                <i className={cardData.icon1}></i>
              </div>
            </div>
            <div className="d-flex mt-2">
              <span
                className={`badge bg-${cardData.color2}-transparent rounded-pill`}
              >
                {cardData.text2}
                <i className={`fe fe-arrow-${cardData.icon2}`}></i>
              </span>
              <Link
                to="#"
                className="text-muted fs-11 ms-auto text-decoration-underline mt-auto"
              >
                view more
              </Link>
            </div>
          </Card.Body>
        </div>
      </div>
    );
  }

  return (<Fragment>
    <Row>
      <Col xl={12}>
        <div className="row row-cols-xxl-5 row-cols-xl-3 row-cols-md-2">
          {cardsData1.map((idx) => {
            return getMockCard(idx);
          })}
          <VendorRevenueCard revenue={stats?.totalRevenue ?? 0} />
          {cardsData2.map((idx) => {
            return getMockCard(idx);
          })}
        </div>
      </Col>
    </Row>
  </Fragment>
  );
}

export default VendorSummaryCard;