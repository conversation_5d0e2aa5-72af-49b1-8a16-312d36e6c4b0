import { FC } from "react";
import { Card } from "react-bootstrap";
import { Link } from "react-router-dom";

interface VendorRevenueCardProps {
  revenue: number;
}

const VendorRevenueCard: FC<VendorRevenueCardProps> = ({ revenue }) => {
  return (
    <div
      className="col card-background flex-fill"
      key={Math.random()}
    >
      <div className="card custom-card">
        <Card.Body>
          <div className="d-flex">
            <div>
              <p className="fw-medium mb-1 text-muted">Total Revenue</p>
              <h3 className="mb-0">${revenue.toFixed(2)}</h3>
            </div>
            <div
              className={`avatar avatar-md br-4 bg-secondary-transparent ms-auto`}
            >
              <i className="bi bi-archive fs-20"></i>
            </div>
          </div>
          <div className="d-flex mt-2">
            <span
              className={`badge bg-success-transparent rounded-pill`}
            >
              +0.00%
              <i className={`fe fe-arrow-success`}></i>
            </span>
            <Link
              to="#"
              className="text-muted fs-11 ms-auto text-decoration-underline mt-auto"
            >
              view more
            </Link>
          </div>
        </Card.Body>
      </div>
    </div>
  );
}

export default VendorRevenueCard;