import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, Row } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import CreatableSelect from "react-select/creatable";
import MediaDropzone from "../../../../components/dropzone/media_dropzone";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import {
  useApproveProductMutation,
  useLazyGetProductByIdQuery
} from "../../../../services/product";
import { getAllErrorMessages } from "../../../../utils/errors";
import { ManagementProducApprovaltDetailsOptionsList } from "./product_approval_details_options";
import { ManagementProductApprovalDetailsVariantList } from "./product_approval_details_variant_list";
import Swal from "sweetalert2";

interface ManagementProductApprovalDetailsProps { }

const ManagementProductApprovalDetails: FC<ManagementProductApprovalDetailsProps> = () => {
  const { id } = useParams();

  const [getProduct] = useLazyGetProductByIdQuery();
  const [approveProduct] = useApproveProductMutation()
  const [mediasUploaded, setMediasUploaded] = useState<TMedia[]>([]);

  const [detailsFormData, setDetailsFormData] = useState<Partial<TProduct>>({});
  const [err, setErr] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [productOptions, setProductOptions] = useState<TProductOption[]>([])
  const [productVariants, setProductVariants] = useState<TProductVariant[]>([]);

  const navigate = useNavigate();
  const returnToListPage = () => {
    navigate("/managements-products-approval");
  };

  const prepareReceivedData = (data) => {
    const details: any = {
      ...data
    };


    if (data.images?.length > 0) {
      setMediasUploaded(data.images.map((image) => {
        return { url: image.src || image.url }
      }))
    }

    if (data.options) {
      setProductOptions(data.options)
    }

    if (data.variants) {
      setProductVariants(data.variants)
    }

    setDetailsFormData(details);
  }

  useEffect(() => {
    setIsLoading(true);
    getProduct(id || "")
      .unwrap()
      .then((res) => {
        prepareReceivedData(res.pendingChanges)
      })
      .catch((error) => {        
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  const handleProductApproval = (approval: 'approve' | 'reject') => {
    setIsLoading(true)
    approveProduct({ id, approval })
      .unwrap()
      .then((res) => {
        console.log(res);
        returnToListPage()
      })
      .catch((error) => Swal.fire('Error!', getAllErrorMessages(error).messages[0], 'error'))
      .finally(() => setIsLoading(false))
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Product Details"
                route="/managements-products-approval/"
              />
            </Card.Header>
            <div>
              {err?.messages?.map((message: string, index: number) => (
                <Alert key={index} variant="danger">
                  {message}
                </Alert>
              ))}
            </div>
            <Card.Body>
              <Row>
                <Col lg={6}>
                  <Row>
                    <Col lg={6} className="mb-3">
                      <Form.Label>Product Name</Form.Label>
                      <Form.Control
                        disabled
                        value={detailsFormData?.title || ""}
                      />
                    </Col>

                    <Col lg={3} className="mb-3">
                      <Form.Label>Status</Form.Label>
                      <Form.Control
                        disabled
                        value={detailsFormData?.status?.toUpperCase() || ""}
                      />
                    </Col>

                    <Col lg={3} className="mb-3">
                      <Form.Label>Vendor</Form.Label>
                      <Form.Control
                        disabled
                        value={detailsFormData?.vendor?.name || ""}
                      />
                    </Col>
                  </Row>
                  <Row>
                    <Col className="mb-3">
                      <Form.Label>Product Category</Form.Label>
                      <Form.Control
                        disabled
                        value={detailsFormData?.category?.name || ""}
                      />
                    </Col>
                    <Col className="mb-3">
                      <Form.Label>Product Type</Form.Label>
                      <Form.Control
                        disabled
                        value={detailsFormData?.productType?.name || ""}
                      />
                    </Col>
                  </Row>
                  <Col className="mb-3">
                    <Form.Label>Tags</Form.Label>
                    <CreatableSelect
                      isMulti
                      isDisabled
                      value={detailsFormData.tags?.map((tag) => ({ label: tag.name, value: tag }))}
                    />
                  </Col>
                </Col>
                <Col lg={6}>
                  <MediaDropzone
                    disabled
                    uploadedFiles={mediasUploaded}
                    uploadFunction={() => { }}
                    removeFile={() => { }}
                  />
                </Col>
              </Row>
              <Col>
                <Form.Label>Description</Form.Label>
                <div dangerouslySetInnerHTML={{ __html: detailsFormData.description || "" }}></div>
              </Col>
            </Card.Body>
          </Card>

          <Row>
            <Col lg={4}>
              <ManagementProducApprovaltDetailsOptionsList
                productOptions={productOptions}
              />
            </Col>

            <Col lg={8}>
              <ManagementProductApprovalDetailsVariantList
                productVariants={productVariants}
              />
            </Col>
          </Row>
        </Col>
      </Row>
      <Card className="custom-card">
        <Card.Header>
          <Card.Title></Card.Title>
          <Card.Subtitle>

            <Button
              variant=""
              className="btn-success-light ms-2"
              onClick={() => { handleProductApproval('approve') }}
            >
              Approve<i className="bi bi-check-lg ms-2" />
            </Button>

            <Button
              variant=""
              className="btn-danger-light ms-2"
              onClick={() => { handleProductApproval('reject') }}
            >
              Reject<i className="bi bi-x-lg ms-2" />
            </Button>

            <Button
              variant=""
              className="btn-light border-dark ms-2"
              onClick={() => returnToListPage()}
            >
              Cancel
            </Button>
          </Card.Subtitle>
        </Card.Header>
      </Card>
    </Fragment>
  );
};

export default ManagementProductApprovalDetails;
