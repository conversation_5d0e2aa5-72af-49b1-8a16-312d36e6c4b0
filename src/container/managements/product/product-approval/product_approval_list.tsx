import { debounce } from "lodash";
import moment from "moment";
import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON>ton, Card, Col, Form, OverlayTrigger, Row, Table, Tooltip } from "react-bootstrap";
import { Link } from "react-router-dom";
import LazySelect from "../../../../components/lazy-select/lazy-select";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useLazyListProductQuery } from "../../../../services/product";
import { useLazySelectProductVendorQuery } from "../../../../services/product/product_vendor";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";

interface ManagementProductApprovalListProps { }

const ManagementProductApprovalList: FC<ManagementProductApprovalListProps> = () => {
  const [page, setPage] = useState(1);
  const [lastPage, setLastPage] = useState(20);
  const [total, setTotal] = useState(20)
  const [search, setSearch] = useState("");
  const setDebouncedSearch = debounce((value) => setSearch(value), 500)

  const [filters, setFilters] = useState({
    status: '',
    vendorId: '',
    pendingApproval: ''
  })
  const [filtersChange, setFiltersChange] = useState(false)

  const [isLoading, setIsLoading] = useState(false);

  const [trigger] = useLazyListProductQuery();
  const [selectVendor] = useLazySelectProductVendorQuery()

  const [products, setProducts] = useState<TProduct[]>([]);

  useEffect(() => {
    setIsLoading(true)
    // @ts-ignore
    trigger({ page, search, ...filters, })
      .unwrap()
      .then((res) => {
        setProducts(res.data || [])
        setLastPage(res.meta.lastPage)
        setTotal(res.meta.total)
      })
      .finally(() => setIsLoading(false))
  }, [page, search, filtersChange])

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Products Approval Management"
                route=""
              ></CardHeaderWithBack>
            </Card.Header>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                <Row>
                  <Col lg={8} className="mb-3">
                    <Form.Control
                      type="search"
                      placeholder="Search"
                      onChange={(e) => setDebouncedSearch(e.target.value)}
                    />
                  </Col>

                  <Col lg={4} className="mb-3">
                    <LazySelect
                      isClearable
                      selectionFunction={selectVendor}
                      label={value => value.name}
                      getSelectedOptions={(value) => {
                        setFilters({ ...filters, vendorId: value?.id })
                        setFiltersChange(!filtersChange)
                      }}
                    />
                  </Col>
                </Row>
                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th>Image</th>
                      <th>Title</th>
                      <th>Vendor</th>
                      <th>Status</th>
                      <th>Updated</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.map((product: TProduct) => (
                      <Fragment key={product.id}>
                        <ReadOnlyRow
                          product={product}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>
                <PaginationBar
                  page={page}
                  setPage={setPage}
                  lastPage={lastPage}
                  total={total}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

const ReadOnlyRow = ({
  product,
}: any) => {

  return (
    <tr>
      <td
        style={{
          textAlign: "center",
          padding: "10px",
          width: "10px",
          height: "10px",
        }}>
        <p className="avatar avatar-xxl bg-dark-transparent my-auto">
          {
            product.image?.src
              ?
              <img
                src={product.image.src || ""}
                style={{
                  display: "block",
                  margin: "0 auto",
                  maxWidth: "100%",
                  height: "100%",
                  objectFit: "cover",
                }}
              />
              : "IMG"
          }
        </p>
      </td>
      <td>
        <Link to={product.id}>
          {product.title}
        </Link>
      </td>
      <td>{product.vendor?.name}</td>
      <td>
        <div>
          {product.status == 'active' ? (
            <span className="badge bg-success rounded-pill">
              Active
            </span>
          ) : product.status == 'draft' ? (
            <span className="badge bg-warning rounded-pill">
              Draft
            </span>
          ) : product.status == 'archived' ? (

            <span className="badge bg-dark rounded-pill">
              Archived
            </span>
          ) : null
          }
          <div>
            {
              product.pendingApproval == 0
                ? <span className="badge bg-success-transparent rounded-pill">Approved</span>
                : product.pendingApproval == 1 ?
                  <span className="badge bg-danger-transparent rounded-pill">Rejected</span>
                  : product.pendingApproval
                    ? <span className="badge bg-primary-transparent rounded-pill">Pending</span>
                    : ""
            }
          </div>
        </div>
      </td>
      <td>
        <div>
          {moment(product.updatedAt).format("YYYY-MM-DD")}
        </div>
        <div>
          {moment(product.updatedAt).format("HH:mm:ss")}
        </div>
      </td>
      <td>
        <OverlayTrigger placement="top" overlay={<Tooltip>Edit</Tooltip>}>
          <Link to={product.id}>
            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.PRODUCT)}
              variant="primary-light"
              className="btn btn-warning-light btn-sm ms-2"
            >
              <span className="ri-edit-line fs-14"></span>
            </Button>
          </Link>
        </OverlayTrigger>

      </td>
    </tr>
  );
};
export default ManagementProductApprovalList;
