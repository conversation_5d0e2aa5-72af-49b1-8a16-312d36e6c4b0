import { FC, Fragment, useEffect, useState } from "react"
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Row } from "react-bootstrap";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { useParams } from "react-router-dom";
import { useAddCommissionGroupMutation, useDeleteCommissionGroupMutation, useLazyGetAffiliateTierByIdQuery, useLazyGetCommissionGroupsQuery } from "../../../services/affiliation/affiliatie-tier";
import { getAllErrorMessages } from "../../../utils/errors";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { hasPermission } from "../../../utils/authorization";
import TierCommissionableGroup from "./tier-commissionable-group";
import NewCommissionGroupDialog from "../../../components/dialog/new-commission-group-dialog";

interface TierAppliedProductsProps { }

interface ErrorType {
  messages?: string[];
}

const TierCommissionableProducts: FC<TierAppliedProductsProps> = () => {
  const { tierId } = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<ErrorType>({});
  const [tier, setTier] = useState<TAffiliateTier>();
  const [commissionGroups, setCommissionGroups] = useState<TAffiliateTierCommissionGroup[]>([]);
  const [showNewCommissionGroupDialog, setShowNewCommissionGroupDialog] = useState<boolean>(false);

  const [getTierById] = useLazyGetAffiliateTierByIdQuery();
  const [getCommissionGroups] = useLazyGetCommissionGroupsQuery();
  const [addCommissionGroup] = useAddCommissionGroupMutation();
  const [deleteCommissionGroup] = useDeleteCommissionGroupMutation();

  useEffect(() => {
    loadTierData(tierId);
  }, [tierId]);

  const loadTierData = (tierId) => {
    if (!tierId) return;
    setIsLoading(true);

    getTierById({ id: tierId })
      .unwrap()
      .then((res) => {
        setTier(res);
        loadCommissionGroups(res);
        setErr({});
      })
      .catch((error) => setErr(getAllErrorMessages(error)))
      .finally(() => setIsLoading(false));
  };

  const loadCommissionGroups = (tier) => {
    if (!tier) {
      setErr({ messages: ['Missing tier data'] });
      return;
    }

    setIsLoading(true);
    getCommissionGroups(tier.id)
      .unwrap()
      .then((res) => {
        const defaultGroup = res.find(grp => grp.commissionRate === tier.defaultCommission);
        const remainingGroups = res.filter(grp => grp.commissionRate !== tier.defaultCommission);
        setCommissionGroups(defaultGroup ? [defaultGroup, ...remainingGroups] : remainingGroups);
      })
      .catch((error) => setErr(getAllErrorMessages(error)))
      .finally(() => setIsLoading(false));
  }

  const handleAddCommissionGroup = (newCommissionRate: number) => {
    if (!tier) {
      setErr({ messages: ['Missing tier data'] });
      return;
    }

    setIsLoading(true);
    addCommissionGroup({
      id: tier.id,
      commissionRate: newCommissionRate
    })
      .unwrap()
      .then((res) => {
        setCommissionGroups([
          ...commissionGroups,
          res
        ]);
      })
      .catch((error) => setErr(getAllErrorMessages(error)))
      .finally(() => setIsLoading(false));
  }

  const handleDeleteCommissionGroup = (groupId: string) => {
    setIsLoading(true);
    deleteCommissionGroup({
      tierId: tierId!,
      groupId
    })
      .unwrap()
      .then(() => {
        setCommissionGroups(commissionGroups.filter(grp => grp.id !== groupId));
      })
      .catch((error) => setErr(getAllErrorMessages(error)))
      .finally(() => setIsLoading(false));
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Row>
        <Col xl={12}>
          <Card>
            <Card.Header>
              <CardHeaderWithBack
                title='Commissionable Products'
                route=''
              />
            </Card.Header>
            <Card.Body>

              <div className="app-container">

                {err?.messages?.map((message: string, index: number) => (
                  <Alert key={index} variant="danger">
                    {message}
                  </Alert>
                ))}

                <Row
                  style={{
                    marginBlockEnd: "1.5rem",
                  }}
                >
                  <Col xl={4} className="mb-4">
                    <Card.Header className="justify-content-between">
                      <Card.Title>Tier</Card.Title>
                    </Card.Header>

                    <Card.Body>
                      <div className="row row-cols-sm-2 row-cols-xxl-5 g-0 ecommerce-cards">
                        <div className="col d-flex p-4 tx-white pos-relative">
                          <div className="flex-1">
                            <span className="text-xl fw-semibold">{tier?.tier}</span>
                          </div>
                        </div>
                      </div>
                    </Card.Body>
                  </Col>

                  <Col xl={4} className="mb-4">
                    <Card.Header className="justify-content-between">
                      <Card.Title>Title</Card.Title>
                    </Card.Header>

                    <Card.Body>
                      <div className="row row-cols-sm-2 row-cols-xxl-5 g-0 ecommerce-cards">
                        <div className="col d-flex p-4 tx-white pos-relative">
                          <div className="flex-1">
                            <span className="text-xl fw-semibold">{tier?.title}</span>
                          </div>
                        </div>
                      </div>
                    </Card.Body>
                  </Col>

                  <Col xl={4} className="mb-4">
                    <Card.Header className="justify-content-between">
                      <Card.Title>Commission Rate</Card.Title>
                    </Card.Header>

                    <Card.Body>
                      <Row>
                        <Col xl={12}>
                          <div className="row row-cols-sm-2 row-cols-xxl-5 g-0 ecommerce-cards">
                            <div className="col d-flex p-4 tx-white pos-relative">
                              <div className="flex-1">
                                <span className="text-xl fw-semibold">{(tier?.defaultCommission || 0) * 100}%</span>
                              </div>
                            </div>
                          </div>
                        </Col>
                      </Row>
                    </Card.Body>
                  </Col>
                </Row>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <div className="d-flex justify-content-center">
        <Button
          hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
          variant="primary-light"
          className="m-2"
          onClick={() => { setShowNewCommissionGroupDialog(true) }}
        >
          Add New Group<i className="bi bi-plus-lg ms-2"></i>
        </Button>
      </div>

      {
        commissionGroups.map((grp) => (
          <TierCommissionableGroup
            tierId={tier?.id || ''}
            commissionGroup={grp}
            isDefault={(tier?.defaultCommission || 0) === grp.commissionRate}
            onDeleteGroup={handleDeleteCommissionGroup}
          />
        ))
      }

      <NewCommissionGroupDialog
        isShow={showNewCommissionGroupDialog}
        onHide={() => { setShowNewCommissionGroupDialog(false) }}
        handleAddCommissionGroup={handleAddCommissionGroup}
      />
    </Fragment>
  );
}

export default TierCommissionableProducts;