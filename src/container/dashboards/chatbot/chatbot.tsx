import { FC, useEffect, useRef, useState, KeyboardEvent } from "react"
import {
    Button,
    Card,
    Form,
    InputGroup,
    ListGroup,
    Spinner,
} from "react-bootstrap"
import {
    useLazyGetChatRoomQuery,
    useCreateChatBotMessageMutation,
    useClearChatMessagesMutation,
    useGetChatMessagesQuery,
    useAnnotateChatMessageMutation,
} from "../../../services/chatbot/chatbot.ts"

interface ChatMessage {
    id: string
    content: string
    assistantId?: string | null
    adminId?: string | null
    role: "user" | "bot" | "system"
    sentAt?: string
    attributes?: Record<string, any>
    annotatedChatMessageId?: string | null
    _localAnnotation?: string
}

const toMillis = (date?: string) => {
    const time = date ? Date.parse(date) : NaN
    return Number.isNaN(time) ? 0 : time
}

const Chatbot: FC = () => {
    const [roomId, setRoomId] = useState<string | null>(null)
    const [input, setInput] = useState("")
    const [messages, setMessages] = useState<ChatMessage[]>([])
    const scrollRef = useRef<HTMLDivElement>(null)

    /* inline annotation */
    const [annotatingId, setAnnotatingId] = useState<string | null>(null)
    const [annotationText, setAnnotationText] = useState("")

    /* ───────────── queries & mutations ───────────── */
    const [initRoom, { data: roomData, isFetching: isRoomLoading }] =
        useLazyGetChatRoomQuery()
    const [sendMessage, { isLoading: isSending }] =
        useCreateChatBotMessageMutation()
    const [clearMessages, { isLoading: isClearing }] =
        useClearChatMessagesMutation()
    const [annotate, { isLoading: isAnnotating }] =
        useAnnotateChatMessageMutation()


    const toChatMessage = (message: any): ChatMessage => ({
        id:      message.id,
        content: message.content ?? "",
        role:    (message.role ?? "system") as ChatMessage["role"],
        sentAt:  message.sentAt,
        assistantId: message.assistantId ?? null,
        adminId:     message.adminId ?? null,
        attributes:  message.attributes ?? {},
        annotatedChatMessageId: message.annotatedChatMessageId ?? null,
        _localAnnotation: message._localAnnotation,
    })


    const { data: fetched, isFetching } = useGetChatMessagesQuery(
        { id: roomId!, params: {} },
        {
            skip: !roomId,
            pollingInterval: 3000,
            refetchOnFocus: true,
            refetchOnReconnect: true,
        },
    )

    /* ───────────── lifecycle ───────────── */
    useEffect(() => {
        initRoom().unwrap().catch(console.error)
    }, [])

    useEffect(() => {
        if (roomData) setRoomId(roomData.id)
    }, [roomData])

    useEffect(() => {
        if (!fetched) return
        setMessages(prev => {
            const seen = new Set(prev.map(m => m.id))
            const incoming = (fetched as ChatMessage[]).filter(
                message => !seen.has(message.id) && message.content?.trim(),
            )
            if (!incoming.length) return prev
            return [...prev, ...incoming].sort((a, b) => toMillis(a.sentAt) - toMillis(b.sentAt))
        })
    }, [fetched])

    useEffect(() => {
        scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight, behavior: "smooth" })
    }, [messages])


    /* ───────────── helpers ───────────── */
    const pushAndSend = async () => {
        if (!input.trim() || !roomId) return

        const tmpId = `tmp-${Date.now()}`
        const nowIso = new Date().toISOString()
        const temp: ChatMessage = {
            id: tmpId,
            role: "user",
            content: input.trim(),
            sentAt: nowIso,
        }
        setMessages(p => [...p, temp])
        setInput("")

        try {
            const saved = await sendMessage({ roomId, content: temp.content }).unwrap()
            setMessages(p =>
                p
                    .map(m => (m.id === tmpId ? toChatMessage(saved) : m))
                    .sort((a, b) => toMillis(a.sentAt) - toMillis(b.sentAt)),
            )
        } catch (error) {
            console.error(error)
        }
    }

    const onKey = (e: KeyboardEvent<HTMLInputElement>) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault()
            pushAndSend()
        }
    }

    const zapChat = async () => {
        if (!roomId) return
        try {
            await clearMessages(roomId).unwrap()
            setMessages([])
        } catch (e) {
            console.error(e)
        }
    }

    const beginAnnotate = (id: string) => {
        setAnnotatingId(id)
        setAnnotationText("")
    }

    const commitAnnotation = async () => {
        if (!roomId || !annotatingId || !annotationText.trim()) return
        try {
            await annotate({ roomId, originalMessageId: annotatingId, content: annotationText.trim() }).unwrap()
            setMessages(prev =>
                prev.map(message =>
                    message.id === annotatingId ? { ...message, _localAnnotation: annotationText.trim() } : message,
                ),
            )
        } catch (error) {
            console.error(error)
        } finally {
            setAnnotatingId(null)
            setAnnotationText("")
        }
    }

    /* ───────────── render ───────────── */
    return (
        <Card style={{ height: 500, display: "flex", flexDirection: "column" }}>
            {/* header */}
            <Card.Header className="d-flex justify-content-end">
                <Button
                    variant="outline-danger"
                    size="sm"
                    onClick={zapChat}
                    disabled={isClearing || !roomId}
                >
                    {isClearing ? "Clearing…" : "Clear chat"}
                </Button>
            </Card.Header>

            {/* messages */}
            <Card.Body ref={scrollRef} style={{ flex: 1, overflowY: "auto" }}>
                {(isRoomLoading || isFetching) && (
                    <Spinner animation="border" size="sm" className="mb-2" />
                )}

                <ListGroup variant="flush">
                    {messages
                        .filter(m => m.content?.trim())
                        .map(({ id, role, content, _localAnnotation }) => (
                            <ListGroup.Item
                                key={id}
                                className={`border-0 d-flex ${role === "user" ? "justify-content-end" : ""}`}
                                style={{ background: "transparent" }}
                                onDoubleClick={() => role === "bot" && beginAnnotate(id)}
                            >
                                <div style={{ maxWidth: "75%" }}>
                                    <span
                                        className={`d-inline-block px-3 py-2 rounded-3 ${
                                            role === "user"
                                                ? "bg-primary text-white"
                                                : role === "bot"
                                                    ? "bg-light"
                                                    : "bg-secondary text-white"
                                        }`}
                                    >
                                        {content}
                                    </span>

                                    {(_localAnnotation || (role === "system" && content)) && (
                                        <div className="mt-1 small text-muted">
                                            {_localAnnotation ?? content}
                                        </div>
                                    )}

                                    {annotatingId === id && (
                                        <InputGroup size="sm" className="mt-1">
                                            <Form.Control
                                                placeholder="Add a note…"
                                                value={annotationText}
                                                disabled={isAnnotating}
                                                onChange={e => setAnnotationText(e.target.value)}
                                                onKeyDown={e => {
                                                    if (e.key === "Enter") {
                                                        e.preventDefault()
                                                        commitAnnotation()
                                                    }
                                                    if (e.key === "Escape") setAnnotatingId(null)
                                                }}
                                            />
                                            <Button
                                                variant="outline-success"
                                                size="sm"
                                                disabled={isAnnotating || !annotationText.trim()}
                                                onClick={commitAnnotation}
                                            >
                                                ✔
                                            </Button>
                                            <Button
                                                variant="outline-secondary"
                                                size="sm"
                                                onClick={() => setAnnotatingId(null)}
                                            >
                                                ✕
                                            </Button>
                                        </InputGroup>
                                    )}
                                </div>
                            </ListGroup.Item>
                        ))}
                </ListGroup>
            </Card.Body>

            {/* composer */}
            <Card.Footer className="p-2 d-flex">
                <InputGroup className="flex-grow-1 me-2">
                    <Form.Control
                        placeholder="Type your message…"
                        value={input}
                        onChange={e => setInput(e.target.value)}
                        onKeyDown={onKey}
                        disabled={isSending || !roomId}
                    />
                </InputGroup>
                <Button
                    variant="primary"
                    onClick={pushAndSend}
                    disabled={isSending || !roomId}
                >
                    {isSending ? "Sending…" : "Send"}
                </Button>
            </Card.Footer>
        </Card>
    )
}

export default Chatbot;
