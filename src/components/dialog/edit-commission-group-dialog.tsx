import { FC, useEffect, useState } from "react";
import { <PERSON><PERSON>, Button, Form, Modal } from "react-bootstrap";
import { ErrorType } from "../../utils/error_type";
import { useUpdateCommissionGroupMutation } from "../../services/affiliation/affiliatie-tier";
import { getAllErrorMessages } from "../../utils/errors";

interface EditCommissionGroupDialogProps {
  isShow: boolean;
  group: TAffiliateTierCommissionGroup;
  onHide: (group: TAffiliateTierCommissionGroup) => void;
  setIsLoading: (isLoading: boolean) => void;
}

const EditCommissionGroupDialog: FC<EditCommissionGroupDialogProps> = ({ isShow, group, onHide, setIsLoading }) => {
  const [err, setErr] = useState<ErrorType>();
  const [commissionPercent, setCommissionPercent] = useState<number>(0);
  const [updateCommissionGroup] = useUpdateCommissionGroupMutation();

  useEffect(() => {
    if (isShow) {
      setCommissionPercent(group.commissionRate * 100);
    }
  }, [isShow]);

  const handleFormChange = (e) => {
    e.preventDefault();
    setCommissionPercent(e.target.value);
  }

  const handleHide = () => {
    onHide(group);
  }

  const handleConfirm = (e) => {
    e.preventDefault();

    const percent = Number(commissionPercent);
    if (isNaN(percent)) {
      setErr({ messages: ['Please enter a number'] });
      return;
    }

    setIsLoading(true);
    updateCommissionGroup({
      tierId: group.tierId,
      groupId: group.id,
      commissionRate: percent / 100
    })
      .unwrap()
      .then((res) => { onHide(res) })
      .catch((error) => { setErr(getAllErrorMessages(error)) })
      .finally(() => { setIsLoading(false) });
  }

  return (
    <Modal show={isShow} onHide={handleHide}>
      <Form onSubmit={handleConfirm}>
        <Modal.Header closeButton>
          <Modal.Title>Edit Group</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Form.Group className="mb-4">
            <Form.Label>Commission Rate</Form.Label>
            <Form.Control
              type="text"
              name="commissionRate"
              value={commissionPercent}
              onChange={handleFormChange}
            />
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="primary" type='submit'>OK</Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
}

export default EditCommissionGroupDialog;