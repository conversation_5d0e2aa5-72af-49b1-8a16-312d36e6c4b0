import { FC, Fragment, useEffect, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, InputGroup, Modal, Row, Table } from "react-bootstrap";
import { useParams } from "react-router-dom";
import PaginationBar from "../pagination-bar/pagination-bar";
import { hasPermission } from "../../utils/authorization";
import { ACTION, RESOURCE } from "../../utils/constant/authorization";
import { getAllErrorMessages } from "../../utils/errors";
import { EListAction } from "../enums/list-action";
import { useLazyGetNonCommissionProductsQuery, useUpdateProductsOfCommissionGroupMutation } from "../../services/affiliation/affiliatie-tier";
import Swal from "sweetalert2";
import LazySelect from "../lazy-select/lazy-select";
import { useLazySelectCollectionQuery } from "../../services/collection";
import { ErrorType } from "../../utils/error_type";

interface AddProductsToCommissionGroupDialogProps {
  isShow: boolean;
  group: TAffiliateTierCommissionGroup;
  setIsLoading: (isLoading: boolean) => void;
  onHide: () => void;
}

const AddProductsToCommissionGroupDialog: FC<AddProductsToCommissionGroupDialogProps> = ({ isShow, group, setIsLoading, onHide }) => {
  const { tierId } = useParams();

  const [totalProducts, setTotalProducts] = useState(0)
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [err, setErr] = useState<ErrorType>({});
  const [products, setProducts] = useState<TProduct[]>([]);
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);
  const [selectedCollection, setSelectedCollection] = useState<TCollection | null>(null);
  const [isClear, setIsClear] = useState(false);
  const [searchTermFormData, setSearchTermFormData] = useState('')
  const [searchTerm, setSearchTerm] = useState('')

  const [selectCollection] = useLazySelectCollectionQuery();
  const [getNonCommissionProducts] = useLazyGetNonCommissionProductsQuery();
  const [updateProducts] = useUpdateProductsOfCommissionGroupMutation();

  useEffect(() => {
    if (!isShow) return;

    if (!tierId) {
      setErr({ messages: ['Tier ID is required'] });
      return;
    }

    setErr({});
    setIsLoading(true);
    getNonCommissionProducts({
      tierId,
      collectionId: selectedCollection?.id || '',
      searchTerm,
      page,
      limit: 10
    })
      .unwrap()
      .then((res) => {
        setTotalProducts(res?.meta?.total)
        setTotalPages(res?.meta?.lastPage);
        setProducts(res.data || []);
      })
      .catch((error) => setErr(getAllErrorMessages(error)))
      .finally(() => setIsLoading(false));
  }, [isShow, tierId, page, selectedCollection, searchTerm])

  const handleSearch = (e) => {
    e.preventDefault()
    setSearchTerm(searchTermFormData)
  }

  const handleClearSearch = () => {
    setSearchTermFormData('')
    setSearchTerm('')
  }

  const handleCheckboxChange = (productId) => {
    setSelectedProductIds((prevSelected) =>
      prevSelected.includes(productId)
        ? prevSelected.filter((rowId) => rowId !== productId)
        : [...prevSelected, productId]
    );
  };

  const handleAddOneProduct = (product: TProduct) => {
    if (!tierId) {
      setErr({ messages: ['Unknown tier ID'] });
      return;
    }
    if (product === null || product === undefined) {
      setErr({ messages: ['Unknown product'] });
      return;
    }

    Swal.fire({
      title: "Are you sure?",
      text: `Add ${product.title} to commission group?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, add it!",
    })
      .then((result) => {
        if (result.isConfirmed) {

          setErr({});
          setIsLoading(true);
          updateProducts({
            tierId,
            groupId: group.id,
            action: EListAction.ADD,
            productIds: [product.id],
          })
            .unwrap()
            .then(onHide)
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            })
            .finally(() => {
              setIsLoading(false);
            })
        }
      })
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      })
  }

  const handleAddSelectedProducts = () => {
    if (!tierId) {
      setErr({ messages: ['Unknown tier ID'] });
      return;
    }
    if (selectedProductIds.length == 0) {
      setErr({ messages: ['No product is selected'] });
      return;
    }

    Swal.fire({
      title: "Are you sure?",
      text: `Add ${selectedProductIds.length} products to commission group?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, add them!",
    })
      .then((result) => {
        if (result.isConfirmed) {

          setErr({});
          setIsLoading(true);
          updateProducts({
            tierId,
            groupId: group.id,
            action: EListAction.ADD,
            productIds: selectedProductIds
          })
            .unwrap()
            .then(onHide)
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            })
            .finally(() => {
              setIsLoading(false);
            })
        }
      })
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      })
  }

  const handleAddAllProducts = () => {
    if (!tierId) {
      setErr({ messages: ['Unknown tier ID'] });
      return;
    }
    if (products.length === 0) {
      setErr({ messages: ['No product is remaining'] });
      return;
    }

    Swal.fire({
      title: "Are you sure?",
      text: `Add all products to commission group?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, add them!",
    })
      .then((result) => {
        if (result.isConfirmed) {

          setErr({});
          setIsLoading(true);
          updateProducts({
            tierId,
            groupId: group.id,
            action: EListAction.ADD,
            productIds: ['all'],
            collectionId: selectedCollection?.id
          })
            .unwrap()
            .then(onHide)
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            })
            .finally(() => {
              setIsLoading(false);
            })
        }
      })
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      })
  }

  return (
    <Modal show={isShow} onHide={onHide} size="xl">
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <Card.Title>Add Products to Commission Group</Card.Title>
              <div className="px-4 justify-content-end">
                <Button
                  hidden={!hasPermission(ACTION.CREATE, RESOURCE.AFFILIATION)}
                  disabled={selectedProductIds.length === 0}
                  variant="primary-light"
                  className="m-2"
                  onClick={handleAddSelectedProducts}
                >
                  Add Selected Products {`(${selectedProductIds.length})`}<i className="bi bi-plus-lg ms-2"></i>
                </Button>
                <Button
                  hidden={!hasPermission(ACTION.CREATE, RESOURCE.AFFILIATION)}
                  disabled={products.length === 0}
                  variant="primary-light"
                  className="m-2"
                  onClick={handleAddAllProducts}
                >
                  Add All Products {`(${totalProducts})`}<i className="bi bi-plus-lg ms-2"></i>
                </Button>
              </div>
            </Card.Header>
            <Card.Body>

              {err?.messages?.map((message: string, index: number) => (
                <Alert key={index} variant="danger">
                  {message}
                </Alert>
              ))}

              <div className="app-container">
                <div className="mb-3">

                  <Form onSubmit={handleSearch}>

                    <Row>
                      <Col xl={4}>
                        <Row>
                          <Col xl={2} className="d-flex align-items-center">
                            <Form.Label column>Collection</Form.Label>
                          </Col>
                          <Col xl={10}>
                            <LazySelect
                              selectionFunction={selectCollection}
                              label={(value) => value.title}
                              getSelectedOptions={(value) => setSelectedCollection(value)}
                              clearSelected={[isClear, setIsClear]}
                              formatOptionLabel={(value) => (
                                <div className="d-flex align-items-center">
                                  <div className="me-3 lh-1">
                                    <span className="avatar avatar-sm">
                                      <img src={value.value.imageUrl} alt="" />
                                    </span>
                                  </div>
                                  <div className="mb-1 fs-14 fw-semibold">
                                    {value.value.title}
                                  </div>
                                </div>
                              )}
                            />
                          </Col>
                        </Row>
                      </Col>

                      <Col xl={8}>
                        <InputGroup className="mb-3">
                          <Form.Control
                            type="text"
                            placeholder="Search..."
                            value={searchTermFormData}
                            onChange={(e) => setSearchTermFormData(e.target.value)}
                          />
                          <Button variant="light" className="btn btn-light btn-sm" onClick={handleClearSearch}>X</Button>
                          <Button variant="primary" type="submit">Search</Button>
                        </InputGroup>
                      </Col>

                    </Row>

                  </Form>

                </div>
                <Table className="table table-bordered text-nowrap border-bottom" responsive>
                  <thead>
                    <tr>
                      <th className="text-center align-middle">
                        <Form.Check
                          type="checkbox"
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedProductIds(products.map((product) => product.id));
                            } else {
                              setSelectedProductIds([]);
                            }
                          }}
                          checked={selectedProductIds.length === products.length && products.length > 0}
                          style={{ transform: "scale(1.5)" }} // Makes checkbox larger
                        />
                      </th>
                      <th>TITLE</th>
                      <th>ACTION</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.map((product: TProduct) => (
                      <Fragment key={product.id}>
                        <ReadOnlyRow
                          product={product}
                          selectedProductIds={selectedProductIds}
                          handleCheckboxChange={handleCheckboxChange}
                          handleAddOneProduct={handleAddOneProduct}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>

                <PaginationBar
                  page={page}
                  setPage={setPage}
                  lastPage={totalPages}
                />

              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

    </Modal>
  );
}

const ReadOnlyRow = ({
  product,
  selectedProductIds,
  handleCheckboxChange,
  handleAddOneProduct
}: any) => {
  return (
    <tr>
      <td className="text-center align-middle">
        <Form.Check
          type="checkbox"
          onChange={() => handleCheckboxChange(product.id)}
          checked={selectedProductIds.includes(product.id)}
          style={{ transform: "scale(1.5)" }} // Enlarges row checkboxes
        />
      </td>
      <td>{product.title}</td>
      <td style={{ textAlign: "center" }}>
        <Button
          hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
          variant="primary-light"
          onClick={() => { handleAddOneProduct(product) }}
        >
          Add<i className="bi bi-plus-lg ms-2" />
        </Button>
      </td>
    </tr>
  );
};

export default AddProductsToCommissionGroupDialog;