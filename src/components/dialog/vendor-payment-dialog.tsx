import { FC, Fragment, useEffect, useState } from "react";
import { Alert, Button, Form, InputGroup, Modal } from "react-bootstrap";
import { useAddVendorPaymentMutation, useLazyGetAllPaymentMethodsQuery } from "../../services/vendors";
import { getAllErrorMessages } from "../../utils/errors";
import { EPaymentType } from "../enums/payment-type-enum";
import Swal from "sweetalert2";

interface VendorPaymentDialogProps {
  vendor: TVendor;
  show: boolean;
  setIsLoading: (loading: boolean) => void;
  handleClose: (success: boolean) => void;
}

const VendorPaymentDialog: FC<VendorPaymentDialogProps> = ({ vendor, show, setIsLoading, handleClose }) => {
  const [err, setErr] = useState<any>({});
  const [paymentMethods, setPaymentMethods] = useState<TPaymentMethod[]>([]);
  const [amount, setAmount] = useState<number>(0);
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<string>('');

  const [getAllPaymentMethods] = useLazyGetAllPaymentMethodsQuery();
  const [addPayment] = useAddVendorPaymentMutation();

  const loadPaymentMethods = (vendorId: string) => {
    setIsLoading(true);
    setErr({});
    getAllPaymentMethods(vendorId)
      .unwrap()
      .then((receivedMethods) => {
        setPaymentMethods(receivedMethods);
        if (!selectedPaymentMethodId) {
          for (const method of receivedMethods) {
            if (method.isDefault) {
              setSelectedPaymentMethodId(method.id);
            }
          }
        }
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const handleAmountChange = (event: any) => {
    if (event) {
      event.preventDefault();
    }

    const fieldValue = event.target.value;

    if ((isNaN(fieldValue) || Number(fieldValue) < 0))
      return

    setAmount(fieldValue);
  };

  const submitPayment = (e) => {
    e.preventDefault()

    Swal.fire({
      title: "Are you sure?",
      html: `<p>You are recording to pay ${vendor.companyName} for the amount of $${amount}</p><p>An automatic email will be sent to the vendor.</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, pay!",
    })
      .then((result) => {
        if (result.isConfirmed) {

          setErr({});
          setIsLoading(true);
          addPayment({
            vendorId: vendor.id,
            data: {
              amount,
              paymentMethodId: selectedPaymentMethodId
            }
          })
            .unwrap()
            .then(() => {
              handleClose(true);
            })
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            })
            .finally(() => {
              setIsLoading(false);
            })
        }
      })
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      })
  }

  useEffect(() => {
    if (show) {
      loadPaymentMethods(vendor.id);
    }
  }, [show]);

  return (
    <Fragment>
      <Modal
        show={show}
        onHide={() => handleClose(false)}
        size="lg"
        aria-labelledby="contained-modal-title-vcenter"
        centered
      >
        <Form className="" onSubmit={submitPayment}>
          <Modal.Header closeButton>
            <Modal.Title id="contained-modal-title-vcenter">
              Payout
            </Modal.Title>
          </Modal.Header>

          <Modal.Body>
            {err?.messages?.map((message: string, index: number) => (
              <Alert key={index} variant="danger">
                {message}
              </Alert>
            ))}

            <Form.Group className="mb-4">
              <Form.Label>Amount</Form.Label>
              <Form.Control
                type="text"
                name="amount"
                required
                placeholder="0"
                value={amount}
                onChange={handleAmountChange}
                className="form-control mb-2 border"
              />
            </Form.Group>

            <Form.Group className="mb-4">
              <Form.Label>Payment Method</Form.Label>

              {
                (paymentMethods && paymentMethods.length > 0) ?
                  (
                    <InputGroup>
                      {
                        paymentMethods.map((paymentMethod) => (
                          <InputGroup.Text>
                            <span className="input-group-addon">
                              <input type="radio" checked={selectedPaymentMethodId === paymentMethod.id} onChange={(event) => {
                                if (event.target.checked) {
                                  setSelectedPaymentMethodId(paymentMethod.id)
                                }
                              }} /> {paymentMethod.paymentTypeName} {paymentMethod.isDefault ? '(Default)' : ''}</span>
                          </InputGroup.Text>
                        ))
                      }
                    </InputGroup>
                  ) :
                  (<p className="fs-14 text-muted">No payment method is available</p>)
              }
            </Form.Group>

            <Form.Group className="mb-4" style={{ height: '100px' }}>
              {
                (paymentMethods && paymentMethods.length > 0) &&
                (
                  <Fragment>
                    {
                      paymentMethods
                        .filter(paymentMethod => paymentMethod.id === selectedPaymentMethodId)
                        .map(paymentMethod => (
                          <Fragment>
                            {
                              (paymentMethod.paymentType == EPaymentType.PAYPAL) &&
                              (
                                <ul className="list-unstyled order-details-list">
                                  <li>
                                    <span className="me-2 text-default fw-semibold">PayPal Email:</span>
                                    <span className="fs-14 text-muted">{paymentMethod.paypalDetail.paypalEmail}</span>
                                  </li>
                                </ul>
                              )
                            }
                            {
                              (paymentMethod.paymentType == EPaymentType.OTHER) &&
                              (
                                <ul className="list-unstyled order-details-list">
                                  <li>
                                    <span className="me-2 text-default fw-semibold">Note:</span>
                                    <span className="fs-14 text-muted">{paymentMethod.otherPaymentDetail.note}</span>
                                  </li>
                                </ul>
                              )
                            }
                          </Fragment>
                        ))
                    }
                  </Fragment>
                )
              }
            </Form.Group>
          </Modal.Body>

          <Modal.Footer>
            <Button
              variant="outline-primary"
              onClick={() => handleClose(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              style={{ width: '90px' }}
              disabled={paymentMethods?.length === 0 || amount <= 0}
              type='submit'
            >
              Pay
            </Button>
          </Modal.Footer>

        </Form>
      </Modal>
    </Fragment>
  );
}

export default VendorPaymentDialog;