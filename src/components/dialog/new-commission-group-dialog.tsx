import { FC, useState } from "react";
import { <PERSON><PERSON>, Button, Form, Modal } from "react-bootstrap";
import { ErrorType } from "../../utils/error_type";

interface NewCommissionGroupDialogProps {
  isShow: boolean;
  onHide: () => void;
  handleAddCommissionGroup: (newCommissionRate: number) => void;
}

const NewCommissionGroupDialog: FC<NewCommissionGroupDialogProps> = ({ isShow, onHide, handleAddCommissionGroup }) => {
  const [err, setErr] = useState<ErrorType>();
  const [commissionPercent, setCommissionPercent] = useState<number>(0);

  const handleFormChange = (e) => {
    e.preventDefault();
    setCommissionPercent(e.target.value);
  }

  const handleConfirm = (e) => {
    e.preventDefault();

    const percent = Number(commissionPercent);
    if (isNaN(percent)) {
      setErr({ messages: ['Please enter a number'] });
      return;
    }

    handleAddCommissionGroup(commissionPercent / 100);
    onHide();
  }

  return (
    <Modal show={isShow} onHide={onHide}>
      <Form onSubmit={handleConfirm}>
        <Modal.Header closeButton>
          <Modal.Title>New Group</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Form.Group className="mb-4">
            <Form.Label>Commission Rate</Form.Label>
            <Form.Control
              type="text"
              name="commissionRate"
              value={commissionPercent}
              onChange={handleFormChange}
            />
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="primary" type='submit'>OK</Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
}

export default NewCommissionGroupDialog;