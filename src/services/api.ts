import type { AxiosError, AxiosRequestConfig } from "axios";

import axios, { AxiosHeaders } from "axios";
import {
  createApi,
  type BaseQueryFn,
  retry,
} from "@reduxjs/toolkit/query/react";
import { ACCESS_TOKEN } from "../utils/constant/local-storage";

const whitelistAPI = ["auth/login"];

const cancelTokenSource = axios.CancelToken.source();

export const axiosBaseQuery =
  (): BaseQueryFn<
    {
      url: string;
      method: AxiosRequestConfig["method"];
      data?: AxiosRequestConfig["data"];
      params?: AxiosRequestConfig["params"];
      customHeaders?: { [key: string]: any };
      headersOverride?: { [key: string]: any };
      axiosConfig?: { [key: string]: any };
      enableToast?: boolean;
    },
    unknown,
    unknown
  > =>
  async (
    {
      url,
      method,
      data,
      params,
      customHeaders = {},
      headersOverride,
      axiosConfig,
    },
    { signal }
  ) => {
    try {
      axios.defaults.baseURL = import.meta.env.VITE_APP_API_URL;
      const accessToken = localStorage.getItem(ACCESS_TOKEN);
      // ken = (getState() as RootState).auth.Token
      let headers = new AxiosHeaders();

      if (accessToken && !whitelistAPI.some((wl) => wl === url)) {
        headers.set("authorization", `Bearer ${accessToken}`);
      }
      if (Object.keys(customHeaders).length > 0) {
        // Append custom headers
        Object.entries(customHeaders).forEach(([key, value]) => {
          headers[key] = value;
        });
      }
      if (headersOverride) headers = new AxiosHeaders();

      console.log(`Data: ${data}, URL: ${url}, METHOD: ${method}`);
      signal.addEventListener("abort", () => {
        cancelTokenSource.cancel();
      });

      const result = await axios({
        url: url,
        method,
        data,
        params,
        headers,
        cancelToken: cancelTokenSource.token,
        ...axiosConfig,
      });

      return { data: result.data };
    } catch (axiosError) {
      if (axios.isCancel(axiosError)) {
        return { error: { status: "CANCELLED" } };
      }

      const error = axiosError as AxiosError;

      if (error.response?.status === 401) {
        cancelTokenSource.cancel("Unauthorized");
        localStorage.clear();
        document.location.href = "/";
      } else if (error.code == "ERR_NETWORK") {
        console.error("Network error: ", error.message);
        return {
          error: {
            status: error.response?.status || 503,
            data: "Network error, please check your connection.",
          },
        };
      }

      let message = error.message;
      const dataError = error.response?.data as any;
      if (dataError) {
        message = dataError.message || dataError;
      }

      return {
        error: {
          status: error.response?.status,
          data: dataError,
          message,
        },
      };
    }
  };

export const apiService = createApi({
  baseQuery: retry(axiosBaseQuery(), { maxRetries: 0 }),
  refetchOnFocus: true,
  tagTypes: ["Auth", "Profile", "Event", "Events"],
  endpoints: () => ({}),
});
