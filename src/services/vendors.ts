import { apiService } from "./api";
import { EApprovalStatus } from "../components/enums/approval-status-enum";
const ENDPOINT = "v1/admin/vendors";

export const vendorsService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getVendorsSettings: build.query<any, void>({
      query: () => ({
        url: `${ENDPOINT}/settings`,
        method: "GET",
      }),
    }),

    getVendorsLists: build.query<
      TReponsePaging<TVendor>,
      TQueryAPI
    >({
      query: (params) => ({
        url: ENDPOINT + "/",
        method: "GET",
        params,
      })
    }),

    getVendorById: build.query<TVendor, string>({
      query: (id) => ({
        url: `${ENDPOINT}/${id}`,
        method: "GET",
      }),
    }),

    getVendorStats: build.query<TVendorStats, string>({
      query: (id) => ({
        url: `${ENDPOINT}/${id}/stats`,
        method: "GET"
      }),
    }),

    updateVendor: build.mutation<TVendor, { id: string; data: Partial<TVendor> }>({
      query: ({ id, data }) => ({
        url: `${ENDPOINT}/${id}`,
        method: "PUT",
        data,
      }),
    }),

    deleteVendor: build.mutation<any, string>({
      query: (id) => ({
        url: `${ENDPOINT}/${id}`,
        method: "DELETE",
      }),
    }),

    getVendorEarnings: build.query<TReponsePaging<TVendorEarning>, TQueryAPI>({
      query: (params) => ({
        url: `${ENDPOINT}/earnings`,
        method: "GET",
        params,
      }),
    }),

    getVendorEarningById: build.query<TVendorEarning, string>({
      query: (id) => ({
        url: `${ENDPOINT}/earnings/${id}`,
        method: "GET",
      }),
    }),

    updateVendorEarningStatus: build.mutation<TVendorEarning, { id: string; status: EApprovalStatus; rejectionReason?: string }>({
      query: ({ id, status, rejectionReason }) => ({
        url: `${ENDPOINT}/earnings/${id}`,
        method: "PUT",
        data: { status, rejectionReason },
      }),
    }),

    updateVendorEarningAmount: build.mutation<TVendorEarning, { id: string; adjustedAmount: number; adjustedReason: string }>({
      query: ({ id, adjustedAmount, adjustedReason }) => ({
        url: `${ENDPOINT}/earnings/${id}`,
        method: "PUT",
        data: { adjustedAmount, adjustedReason },
      }),
    }),

    getVendorPayments: build.query<TReponsePaging<TVendorPayment>, { vendorId: string, params: TQueryAPI }>({
      query: ({ vendorId, params }) => ({
        url: `${ENDPOINT}/${vendorId}/payments`,
        method: "GET",
        params,
      }),
    }),

    addVendorPayment: build.mutation<TVendorPayment, { vendorId: string, data: Partial<TVendorPayment> }>({
      query: ({ vendorId, data }) => ({
        url: `${ENDPOINT}/${vendorId}/payments`,
        method: "POST",
        data,
      }),
    }),

    getAllPaymentMethods: build.query<TPaymentMethod[], string>({
      query: (vendorId) => ({
        url: `${ENDPOINT}/${vendorId}/payment-methods`,
        method: "GET",
      }),
    }),
  })
});

export const {
  useLazyGetVendorsSettingsQuery,
  useLazyGetVendorsListsQuery,
  useLazyGetVendorByIdQuery,
  useLazyGetVendorStatsQuery,
  useUpdateVendorMutation,
  useDeleteVendorMutation,

  useLazyGetVendorEarningsQuery,
  useLazyGetVendorEarningByIdQuery,
  useUpdateVendorEarningStatusMutation,
  useUpdateVendorEarningAmountMutation,

  useLazyGetVendorPaymentsQuery,
  useAddVendorPaymentMutation,

  useLazyGetAllPaymentMethodsQuery,
} = vendorsService;