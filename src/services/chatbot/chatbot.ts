import {apiService} from '../api'
import {TChatResource, TChatSuggestion, TChatTokenResponse} from "../../types/chat.ts";

const ENDPOINT = "v1/admin/chat-bot"

export const chatBotService = apiService.injectEndpoints({
    endpoints: (build) => ({

        getChatRoom: build.query<TChatRoom, void>({
            query: () => ({
                url: ENDPOINT,
                method: 'GET',
            }),
            transformResponse: (raw: TChatRoom) => raw,
        }),


        listChatRooms : build.query<TReponsePaging<TChatRoom>, TQueryAPI>({
            query: (params) => ({
                url: ENDPOINT,
                method: "GET",
                params
            }),
            transformResponse: (raw: TReponsePaging<TChatRoom>) => {
                const dataWithIndex = raw.data?.map((item, index) => ({
                    ...item,
                    _rowIndex: index + 1,
                }))
                return {...raw, data: dataWithIndex || []}
            }
        }),

        listChatResources: build.query<TChatResource[], void>({
            query: () => ({
                url: `${ENDPOINT}/resources`,
                method: "GET"
            }),
            transformResponse: (raw: TChatResource[]) => raw,
        }),

        getChatToken: build.query<TChatTokenResponse, string>({
            query: (roomId) => ({
                url: `${ENDPOINT}/${roomId}/token`,
                method: "GET"
            }),
        }),

        getChatSuggestions: build.query<TChatSuggestion[], string>({
            query: (roomId) => ({
                url: `${ENDPOINT}/${roomId}/suggestions`,
                method: "GET",
            }),
            transformResponse: (raw: TChatSuggestion[]) => raw,
        }),

        getChatMessages: build.query<TChatMessage[], {id: string, params?: TQueryAPI}>({
            query: ({id, params}) => ({
                url: `${ENDPOINT}/${id}/messages`,
                method: "GET",
                params,
            }),
            transformResponse: (raw: TReponsePaging<TChatMessage>) => (raw.data ?? []).map((message: any) => ({
                ...message,
                role: message.assistantId ? "bot" : message.adminId ? "user" : "system"
            }) )
                .sort(
                    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
                )
        }),

        createChatBotMessage: build.mutation<TChatMessage, {roomId: string; content: string}>({
            query: ({ roomId, content }) => ({
                url: `${ENDPOINT}/${roomId}/messages`,
                method: "POST",
                data: { content },
            }),
        }),

        clearChatMessages: build.mutation<void, string>({
            query: (id) => ({
                url: `${ENDPOINT}/${id}/messages`,
                method: "DELETE",
            }),
        }),

        annotateChatMessage: build.mutation<TChatMessage, {roomId: string; originalMessageId: string; content: string}>({
            query: ({roomId, originalMessageId, content}) => ({
                url: `${ENDPOINT}/${roomId}/annotateMessages/${originalMessageId}`,
                method: "POST",
                data: { content }
            }),
            transformResponse: (raw: TChatMessage) => ({
                ...raw,
                role: "system",
            })
        })
    })
})

export const {
    useLazyListChatResourcesQuery,
    useLazyGetChatTokenQuery,
    useLazyGetChatSuggestionsQuery,
    useLazyGetChatRoomQuery,
    useCreateChatBotMessageMutation,
    useClearChatMessagesMutation,
    useGetChatMessagesQuery,
    useAnnotateChatMessageMutation,
} = chatBotService